# Bali Home Immo Property Scraping Workflow

## Overzicht
Dit project gebruikt Firecrawl MCP Server om alle rental properties van bali-home-immo.com te scrapen en in een gestructureerd JSON formaat op te slaan.

## Workflow Stappen

### 1. Property URLs Verzamelen
Gebruik `firecrawl_map` om alle URLs te verzamelen:
```
firecrawl_map_mcp-server-firecrawl:
  url: https://bali-home-immo.com/realestate-property/for-rent
  limit: 100
```

### 2. URLs Filteren
Filter de URLs om alleen echte property detail pagina's te krijgen (49 properties gevonden).

### 3. Batch Processing
Verwerk properties in batches van 1-3 URLs per keer met `firecrawl_extract`:

**Schema voor data extractie:**
```json
{
  "type": "object",
  "properties": {
    "title": {"type": "string"},
    "price": {"type": "string"},
    "payment_term": {"type": "string"},
    "location": {"type": "string"},
    "bedrooms": {"type": "integer"},
    "bathrooms": {"type": "integer"},
    "description": {"type": "string"},
    "amenities": {"type": "array", "items": {"type": "string"}},
    "size": {
      "type": "object",
      "properties": {
        "land_size_sqm": {"type": "integer"},
        "building_size_sqm": {"type": "integer"}
      }
    },
    "year_built": {"type": "integer"},
    "property_type": {"type": "string"},
    "property_id": {"type": "string"},
    "status": {"type": "string"},
    "furnishing": {"type": "string"},
    "view": {"type": "string"},
    "electricity_capacity": {"type": "string"},
    "water_source": {"type": "string"},
    "parking": {"type": "string"},
    "facility_layout": {"type": "string"},
    "internet": {"type": "string"},
    "detail_url": {"type": "string"}
  },
  "required": ["title", "price", "location", "bedrooms", "bathrooms", "property_id", "detail_url"]
}
```

### 4. Voorbeeld Firecrawl Extract Commando
```
firecrawl_extract_mcp-server-firecrawl:
  urls: ["https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/2-bedroom-townhouse-for-rent-in-berawa-rf1508"]
  prompt: "Extract comprehensive property details including title, price, payment terms, location, bedrooms, bathrooms, description, amenities, size information, year built, property type, property ID, status, furnishing, and all other relevant property details"
  schema: [schema zoals hierboven]
```

## Sample Resultaten

### Verwerkte Properties (5 van 49):
1. **RF1508** - 2BR Townhouse in Berawa (Price On Request)
2. **BHI620-A** - 1BR Pool Villa in Canggu (IDR 13.150.000/month)
3. **RF7743** - 2BR Villa with Rooftop in Cemagi (IDR 40.000.000/month)
4. **BHI1080** - 2BR Villa in Canggu (IDR 18.750.000/month)
5. **AD019** - 2BR Villa in Pandawa (IDR 19.000.000/month)

### Locatie Verdeling:
- Canggu (verschillende sub-areas): 3 properties
- Cemagi/Seseh: 1 property
- Pandawa: 1 property

### Slaapkamer Verdeling:
- 1 slaapkamer: 1 property
- 2 slaapkamers: 4 properties

## Volgende Stappen

### Voor Complete Dataset:
1. Verwerk alle 49 property URLs in batches
2. Sla elke batch op als `batch_XX_properties.json`
3. Combineer alle batches tot `all_bali_properties.json`
4. Genereer complete analyse rapport

### Batch Processing Commando's:
```bash
python batch_scraper.py batches    # Toon alle batch URLs
python batch_scraper.py combine    # Combineer alle batch bestanden
python batch_scraper.py summary    # Toon samenvatting
```

## Bestanden in Project:
- `property_urls.py` - Alle gevonden URLs
- `batch_scraper.py` - Batch processing utilities
- `sample_bali_properties.json` - Sample van 5 properties
- `batch_01_properties.json` - Eerste batch resultaten
- `firecrawl_property_scraper.py` - Scraper framework

## Data Kwaliteit:
- Alle properties hebben complete basis informatie
- Prijzen variëren van IDR 13M tot 40M per maand
- Meeste properties zijn volledig gemeubileerd
- Alle hebben zwembad en moderne voorzieningen
- Fiber optic internet standaard

## Technische Details:
- Gebruikt Firecrawl MCP Server voor scraping
- AI-powered data extractie met gestructureerd schema
- Respecteert server limits met batch processing
- JSON output voor verdere verwerking
