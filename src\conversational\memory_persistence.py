#!/usr/bin/env python3
"""
Memory Persistence for Conversational Assistant
<PERSON>les session storage and conversation history
"""

import json
import os
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from pathlib import Path
import sqlite3
import uuid
from dataclasses import dataclass, asdict
import asyncio

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ConversationSession:
    """Conversation session data structure"""
    session_id: str
    user_id: Optional[str]
    created_at: datetime
    last_activity: datetime
    conversation_history: List[Dict[str, Any]]
    user_preferences: Dict[str, Any]
    search_history: List[Dict[str, Any]]
    status: str  # 'active', 'completed', 'abandoned'
    contact_info: Optional[str] = None
    notes: Optional[str] = None

class MemoryManager:
    """
    Memory persistence manager for conversation sessions
    """
    
    def __init__(self, db_path: str = "data/conversations.db"):
        """
        Initialize memory manager
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database
        self._init_database()
        
    def _init_database(self):
        """Initialize SQLite database with required tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create sessions table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS conversation_sessions (
                        session_id TEXT PRIMARY KEY,
                        user_id TEXT,
                        created_at TIMESTAMP,
                        last_activity TIMESTAMP,
                        conversation_history TEXT,
                        user_preferences TEXT,
                        search_history TEXT,
                        status TEXT DEFAULT 'active',
                        contact_info TEXT,
                        notes TEXT
                    )
                """)
                
                # Create index for faster queries
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_session_activity 
                    ON conversation_sessions(last_activity)
                """)
                
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_session_status 
                    ON conversation_sessions(status)
                """)
                
                conn.commit()
                logger.info("Database initialized successfully")
                
        except Exception as e:
            logger.error(f"Database initialization error: {str(e)}")
            raise
    
    def create_session(self, user_id: Optional[str] = None) -> str:
        """
        Create new conversation session
        
        Args:
            user_id: Optional user identifier
            
        Returns:
            Session ID
        """
        try:
            session_id = str(uuid.uuid4())
            now = datetime.now()
            
            session = ConversationSession(
                session_id=session_id,
                user_id=user_id,
                created_at=now,
                last_activity=now,
                conversation_history=[],
                user_preferences={},
                search_history=[],
                status='active'
            )
            
            self._save_session(session)
            logger.info(f"Created new session: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating session: {str(e)}")
            raise
    
    def get_session(self, session_id: str) -> Optional[ConversationSession]:
        """
        Retrieve conversation session
        
        Args:
            session_id: Session identifier
            
        Returns:
            ConversationSession or None if not found
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM conversation_sessions 
                    WHERE session_id = ?
                """, (session_id,))
                
                row = cursor.fetchone()
                if not row:
                    return None
                
                return self._row_to_session(row)
                
        except Exception as e:
            logger.error(f"Error retrieving session {session_id}: {str(e)}")
            return None
    
    def update_session(self, session: ConversationSession):
        """
        Update conversation session
        
        Args:
            session: ConversationSession to update
        """
        try:
            session.last_activity = datetime.now()
            self._save_session(session)
            
        except Exception as e:
            logger.error(f"Error updating session {session.session_id}: {str(e)}")
            raise
    
    def add_message(self, session_id: str, role: str, content: str, metadata: Optional[Dict] = None):
        """
        Add message to conversation history
        
        Args:
            session_id: Session identifier
            role: Message role (user, assistant, system)
            content: Message content
            metadata: Optional metadata (function calls, etc.)
        """
        try:
            session = self.get_session(session_id)
            if not session:
                logger.warning(f"Session {session_id} not found for message addition")
                return
            
            message = {
                "role": role,
                "content": content,
                "timestamp": datetime.now().isoformat(),
                "metadata": metadata or {}
            }
            
            session.conversation_history.append(message)
            self.update_session(session)
            
        except Exception as e:
            logger.error(f"Error adding message to session {session_id}: {str(e)}")
    
    def update_preferences(self, session_id: str, preferences: Dict[str, Any]):
        """
        Update user preferences for session
        
        Args:
            session_id: Session identifier
            preferences: User preferences to update
        """
        try:
            session = self.get_session(session_id)
            if not session:
                logger.warning(f"Session {session_id} not found for preference update")
                return
            
            session.user_preferences.update(preferences)
            self.update_session(session)
            
        except Exception as e:
            logger.error(f"Error updating preferences for session {session_id}: {str(e)}")
    
    def add_search_result(self, session_id: str, search_params: Dict[str, Any], results: Dict[str, Any]):
        """
        Add search result to session history
        
        Args:
            session_id: Session identifier
            search_params: Search parameters used
            results: Search results
        """
        try:
            session = self.get_session(session_id)
            if not session:
                logger.warning(f"Session {session_id} not found for search result addition")
                return
            
            search_entry = {
                "timestamp": datetime.now().isoformat(),
                "search_params": search_params,
                "results_count": results.get('total', 0),
                "results_summary": {
                    "total": results.get('total', 0),
                    "sources": results.get('sources', {}),
                    "top_properties": [
                        {
                            "id": prop.get('id'),
                            "title": prop.get('title'),
                            "location": prop.get('location'),
                            "price": prop.get('price')
                        }
                        for prop in results.get('properties', [])[:3]
                    ]
                }
            }
            
            session.search_history.append(search_entry)
            self.update_session(session)
            
        except Exception as e:
            logger.error(f"Error adding search result to session {session_id}: {str(e)}")
    
    def set_contact_info(self, session_id: str, contact_info: str):
        """
        Set contact information for session
        
        Args:
            session_id: Session identifier
            contact_info: Contact information
        """
        try:
            session = self.get_session(session_id)
            if not session:
                logger.warning(f"Session {session_id} not found for contact info update")
                return
            
            session.contact_info = contact_info
            self.update_session(session)
            
        except Exception as e:
            logger.error(f"Error setting contact info for session {session_id}: {str(e)}")
    
    def complete_session(self, session_id: str, notes: Optional[str] = None):
        """
        Mark session as completed
        
        Args:
            session_id: Session identifier
            notes: Optional completion notes
        """
        try:
            session = self.get_session(session_id)
            if not session:
                logger.warning(f"Session {session_id} not found for completion")
                return
            
            session.status = 'completed'
            if notes:
                session.notes = notes
            
            self.update_session(session)
            logger.info(f"Session {session_id} marked as completed")
            
        except Exception as e:
            logger.error(f"Error completing session {session_id}: {str(e)}")
    
    def get_active_sessions(self, limit: int = 50) -> List[ConversationSession]:
        """
        Get active conversation sessions
        
        Args:
            limit: Maximum number of sessions to return
            
        Returns:
            List of active sessions
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM conversation_sessions 
                    WHERE status = 'active'
                    ORDER BY last_activity DESC
                    LIMIT ?
                """, (limit,))
                
                rows = cursor.fetchall()
                return [self._row_to_session(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Error retrieving active sessions: {str(e)}")
            return []
    
    def cleanup_old_sessions(self, days_old: int = 30):
        """
        Clean up old inactive sessions
        
        Args:
            days_old: Sessions older than this many days will be cleaned up
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    DELETE FROM conversation_sessions 
                    WHERE last_activity < ? AND status != 'completed'
                """, (cutoff_date,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                logger.info(f"Cleaned up {deleted_count} old sessions")
                
        except Exception as e:
            logger.error(f"Error cleaning up old sessions: {str(e)}")
    
    def get_session_stats(self) -> Dict[str, Any]:
        """
        Get session statistics
        
        Returns:
            Dictionary with session statistics
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Total sessions
                cursor.execute("SELECT COUNT(*) FROM conversation_sessions")
                total_sessions = cursor.fetchone()[0]
                
                # Active sessions
                cursor.execute("SELECT COUNT(*) FROM conversation_sessions WHERE status = 'active'")
                active_sessions = cursor.fetchone()[0]
                
                # Completed sessions
                cursor.execute("SELECT COUNT(*) FROM conversation_sessions WHERE status = 'completed'")
                completed_sessions = cursor.fetchone()[0]
                
                # Sessions with contact info
                cursor.execute("SELECT COUNT(*) FROM conversation_sessions WHERE contact_info IS NOT NULL")
                sessions_with_contact = cursor.fetchone()[0]
                
                return {
                    "total_sessions": total_sessions,
                    "active_sessions": active_sessions,
                    "completed_sessions": completed_sessions,
                    "sessions_with_contact": sessions_with_contact,
                    "conversion_rate": (sessions_with_contact / total_sessions * 100) if total_sessions > 0 else 0
                }
                
        except Exception as e:
            logger.error(f"Error getting session stats: {str(e)}")
            return {}
    
    def _save_session(self, session: ConversationSession):
        """Save session to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO conversation_sessions 
                    (session_id, user_id, created_at, last_activity, conversation_history, 
                     user_preferences, search_history, status, contact_info, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    session.session_id,
                    session.user_id,
                    session.created_at,
                    session.last_activity,
                    json.dumps(session.conversation_history),
                    json.dumps(session.user_preferences),
                    json.dumps(session.search_history),
                    session.status,
                    session.contact_info,
                    session.notes
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error saving session: {str(e)}")
            raise
    
    def _row_to_session(self, row) -> ConversationSession:
        """Convert database row to ConversationSession"""
        return ConversationSession(
            session_id=row[0],
            user_id=row[1],
            created_at=datetime.fromisoformat(row[2]) if row[2] else datetime.now(),
            last_activity=datetime.fromisoformat(row[3]) if row[3] else datetime.now(),
            conversation_history=json.loads(row[4]) if row[4] else [],
            user_preferences=json.loads(row[5]) if row[5] else {},
            search_history=json.loads(row[6]) if row[6] else [],
            status=row[7] or 'active',
            contact_info=row[8],
            notes=row[9]
        )

# Convenience functions
def create_memory_manager() -> MemoryManager:
    """Create and return memory manager instance"""
    return MemoryManager()

async def get_or_create_session(memory_manager: MemoryManager, session_id: Optional[str] = None) -> str:
    """
    Get existing session or create new one
    
    Args:
        memory_manager: MemoryManager instance
        session_id: Optional existing session ID
        
    Returns:
        Session ID
    """
    if session_id:
        session = memory_manager.get_session(session_id)
        if session:
            return session_id
    
    # Create new session
    return memory_manager.create_session()
