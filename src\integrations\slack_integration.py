#!/usr/bin/env python3
"""
Slack Integration for Sales Handoff
Sends conversation summaries to sales team
"""

import json
import os
import logging
from typing import Dict, Any, Optional
import requests
from datetime import datetime
import asyncio

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SlackIntegration:
    """
    Slack integration for sending property search summaries to sales team
    """
    
    def __init__(self, webhook_url: Optional[str] = None, channel: str = "#property-leads"):
        """
        Initialize Slack integration
        
        Args:
            webhook_url: Slack webhook URL (or set SLACK_WEBHOOK_URL env var)
            channel: Slack channel to send messages to
        """
        self.webhook_url = webhook_url or os.getenv('SLACK_WEBHOOK_URL')
        if not self.webhook_url:
            logger.warning("No Slack webhook URL provided. Set SLACK_WEBHOOK_URL environment variable.")
        
        self.channel = channel
        self.enabled = bool(self.webhook_url)
    
    async def send_lead_summary(self, 
                               conversation_summary: str,
                               user_preferences: Dict[str, Any],
                               session_id: str,
                               user_contact: Optional[str] = None) -> bool:
        """
        Send property search lead to Slack
        
        Args:
            conversation_summary: AI-generated summary of conversation
            user_preferences: Extracted user preferences
            session_id: Unique session identifier
            user_contact: User contact info if provided
            
        Returns:
            True if sent successfully, False otherwise
        """
        if not self.enabled:
            logger.warning("Slack integration not enabled - no webhook URL")
            return False
        
        try:
            # Create rich Slack message
            message = self._create_lead_message(
                conversation_summary,
                user_preferences, 
                session_id,
                user_contact
            )
            
            # Send to Slack
            response = requests.post(
                self.webhook_url,
                json=message,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"Lead sent to Slack successfully - Session: {session_id}")
                return True
            else:
                logger.error(f"Slack API error: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending to Slack: {str(e)}")
            return False
    
    def _create_lead_message(self,
                            summary: str,
                            preferences: Dict[str, Any],
                            session_id: str,
                            contact: Optional[str] = None) -> Dict[str, Any]:
        """Create formatted Slack message for property lead"""
        
        # Extract key preferences
        location = preferences.get('location', 'Not specified')
        bedrooms = preferences.get('bedrooms', 'Not specified')
        max_price = preferences.get('max_price')
        property_type = preferences.get('property_type', 'Not specified')
        term = preferences.get('term', 'Not specified')
        
        # Format price
        price_text = "Not specified"
        if max_price:
            if term == 'purchase':
                price_text = f"Up to USD {max_price:,}"
            else:
                price_text = f"Up to IDR {max_price:,}/month"
        
        # Create timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Build message blocks
        blocks = [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "🏡 New Property Lead"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": f"*Location:*\n{location}"
                    },
                    {
                        "type": "mrkdwn", 
                        "text": f"*Property Type:*\n{property_type}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Bedrooms:*\n{bedrooms}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Budget:*\n{price_text}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Term:*\n{term.title() if term != 'Not specified' else term}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Session ID:*\n{session_id}"
                    }
                ]
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Conversation Summary:*\n{summary}"
                }
            }
        ]
        
        # Add contact info if provided
        if contact:
            blocks.append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Contact Info:*\n{contact}"
                }
            })
        
        # Add special requirements if any
        special_requirements = []
        if preferences.get('pool'):
            special_requirements.append("Swimming pool required")
        if preferences.get('pet_friendly'):
            special_requirements.append("Pet-friendly")
        if preferences.get('furnished'):
            special_requirements.append("Furnished")
        
        if special_requirements:
            blocks.append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Special Requirements:*\n• " + "\n• ".join(special_requirements)
                }
            })
        
        # Add timestamp and actions
        blocks.extend([
            {
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": f"Lead generated: {timestamp}"
                    }
                ]
            },
            {
                "type": "actions",
                "elements": [
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "Contact Lead"
                        },
                        "style": "primary",
                        "value": session_id,
                        "action_id": "contact_lead"
                    },
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "View Properties"
                        },
                        "value": session_id,
                        "action_id": "view_properties"
                    }
                ]
            }
        ])
        
        return {
            "channel": self.channel,
            "username": "Property Assistant",
            "icon_emoji": ":house:",
            "blocks": blocks
        }
    
    async def send_property_match_notification(self,
                                             property_details: Dict[str, Any],
                                             user_session: str) -> bool:
        """
        Send notification when perfect property match is found
        
        Args:
            property_details: Property information
            user_session: User session ID
            
        Returns:
            True if sent successfully
        """
        if not self.enabled:
            return False
        
        try:
            message = {
                "channel": self.channel,
                "username": "Property Assistant",
                "icon_emoji": ":star:",
                "text": f"🌟 Perfect Property Match Found!",
                "attachments": [
                    {
                        "color": "good",
                        "fields": [
                            {
                                "title": "Property",
                                "value": property_details.get('title', 'Unknown'),
                                "short": False
                            },
                            {
                                "title": "Location",
                                "value": property_details.get('location', 'Unknown'),
                                "short": True
                            },
                            {
                                "title": "Price",
                                "value": self._format_price(property_details.get('price', {})),
                                "short": True
                            },
                            {
                                "title": "Session",
                                "value": user_session,
                                "short": True
                            }
                        ]
                    }
                ]
            }
            
            response = requests.post(self.webhook_url, json=message, timeout=10)
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Error sending property match notification: {str(e)}")
            return False
    
    def _format_price(self, price_info: Dict[str, Any]) -> str:
        """Format price for display"""
        if not price_info:
            return "Price not available"
        
        display = price_info.get('display', 0)
        currency = price_info.get('currency', 'IDR')
        price_type = price_info.get('type', 'rental')
        
        formatted = f"{currency} {display:,}"
        if price_type == 'rental':
            formatted += " per month"
        elif price_type == 'purchase':
            formatted += " (purchase)"
        
        return formatted
    
    async def send_error_notification(self, error_message: str, session_id: str) -> bool:
        """Send error notification to Slack"""
        if not self.enabled:
            return False
        
        try:
            message = {
                "channel": self.channel,
                "username": "Property Assistant",
                "icon_emoji": ":warning:",
                "text": f"⚠️ System Error - Session: {session_id}",
                "attachments": [
                    {
                        "color": "danger",
                        "text": error_message,
                        "ts": datetime.now().timestamp()
                    }
                ]
            }
            
            response = requests.post(self.webhook_url, json=message, timeout=10)
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Error sending error notification: {str(e)}")
            return False
    
    def test_connection(self) -> bool:
        """Test Slack webhook connection"""
        if not self.enabled:
            logger.warning("Slack integration not enabled")
            return False
        
        try:
            test_message = {
                "channel": self.channel,
                "username": "Property Assistant",
                "icon_emoji": ":white_check_mark:",
                "text": "✅ Slack integration test successful!"
            }
            
            response = requests.post(self.webhook_url, json=test_message, timeout=10)
            success = response.status_code == 200
            
            if success:
                logger.info("Slack connection test successful")
            else:
                logger.error(f"Slack connection test failed: {response.status_code}")
            
            return success
            
        except Exception as e:
            logger.error(f"Slack connection test error: {str(e)}")
            return False

# Convenience function for easy import
async def send_lead_to_slack(summary: str, preferences: Dict[str, Any], session_id: str, contact: str = None) -> bool:
    """
    Convenience function to send lead to Slack
    
    Args:
        summary: Conversation summary
        preferences: User preferences
        session_id: Session identifier
        contact: User contact info
        
    Returns:
        True if sent successfully
    """
    slack = SlackIntegration()
    return await slack.send_lead_summary(summary, preferences, session_id, contact)
