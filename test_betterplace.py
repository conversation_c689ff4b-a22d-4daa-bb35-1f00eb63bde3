#!/usr/bin/env python3
"""
Test BetterPlace Integration
"""

import sys
import os
sys.path.append('src')

from data_ingestion.sources.betterplace import get_source_config, get_property_urls

def test_betterplace_config():
    """Test BetterPlace configuration"""
    print("🧪 TESTING BETTERPLACE CONFIGURATION")
    print("=" * 40)
    
    # Test config
    config = get_source_config()
    print(f"✅ Source Name: {config.name}")
    print(f"✅ Base URL: {config.base_url}")
    print(f"✅ Listing Pages: {len(config.listing_pages)}")
    for i, page in enumerate(config.listing_pages, 1):
        print(f"   {i}. {page}")
    
    # Test URLs
    urls = get_property_urls()
    print(f"\n✅ Sample Property URLs: {len(urls)}")
    for i, url in enumerate(urls, 1):
        print(f"   {i}. {url}")
    
    print(f"\n✅ Custom Prompt Length: {len(config.custom_prompt)} characters")
    print(f"✅ Max Depth: {config.max_depth}")
    print(f"✅ Follow Links: {config.follow_links}")

def test_data_comparison():
    """Compare data from both sources"""
    print("\n🔍 DATA COMPARISON")
    print("=" * 40)
    
    # Sample data from both sources
    bali_sample = {
        "source": "bali_home_immo",
        "focus": "Rentals",
        "price_format": "IDR monthly",
        "unique_fields": ["pet_policy", "monthly_costs", "sublease_allowed"],
        "sample_price": "IDR 19,000,000/month"
    }
    
    betterplace_sample = {
        "source": "betterplace", 
        "focus": "Investment",
        "price_format": "IDR total + USD",
        "unique_fields": ["ownership_type", "lease_duration", "price_per_sqm"],
        "sample_price": "IDR 4,866,049,227 (USD 299,000)"
    }
    
    print("📊 BALI HOME IMMO:")
    for key, value in bali_sample.items():
        print(f"   {key}: {value}")
    
    print("\n📊 BETTERPLACE:")
    for key, value in betterplace_sample.items():
        print(f"   {key}: {value}")
    
    print("\n🎯 COMPLEMENTARY COVERAGE:")
    print("   • Bali Home Immo: Rental market with detailed monthly costs")
    print("   • BetterPlace: Investment market with ownership & ROI data")
    print("   • Combined: Complete Bali property ecosystem")

def test_output_format():
    """Show expected output format"""
    print("\n📋 EXPECTED OUTPUT FORMAT")
    print("=" * 40)
    
    expected_output = {
        "unified_format": {
            "common_fields": [
                "title", "price", "location", "bedrooms", "bathrooms",
                "property_type", "property_id", "images", "detail_url"
            ],
            "source_specific": "Stored in JSON field per source"
        },
        "api_response": {
            "total_properties": "Combined from all sources",
            "sources": ["bali_home_immo", "betterplace"],
            "filters": {
                "by_source": "Filter by rental vs investment",
                "by_price": "Unified price filtering",
                "by_location": "Cross-source location search"
            }
        }
    }
    
    print("✅ UNIFIED FORMAT:")
    print(f"   Common Fields: {len(expected_output['unified_format']['common_fields'])}")
    for field in expected_output['unified_format']['common_fields']:
        print(f"     • {field}")
    
    print(f"\n✅ SOURCE-SPECIFIC DATA:")
    print(f"   Storage: {expected_output['unified_format']['source_specific']}")
    
    print(f"\n✅ API CAPABILITIES:")
    print(f"   Total Properties: {expected_output['api_response']['total_properties']}")
    print(f"   Sources: {expected_output['api_response']['sources']}")
    print("   Filters:")
    for filter_type, description in expected_output['api_response']['filters'].items():
        print(f"     • {filter_type}: {description}")

def main():
    """Run all tests"""
    print("🚀 BETTERPLACE INTEGRATION TEST")
    print("=" * 50)
    
    test_betterplace_config()
    test_data_comparison()
    test_output_format()
    
    print("\n" + "=" * 50)
    print("✅ ALL TESTS COMPLETED")
    print("=" * 50)
    print("BetterPlace integration is ready!")
    print("Next steps:")
    print("1. Test actual data extraction with Firecrawl")
    print("2. Implement URL discovery for search results")
    print("3. Add more property sources")
    print("4. Build unified API layer")

if __name__ == "__main__":
    main()
