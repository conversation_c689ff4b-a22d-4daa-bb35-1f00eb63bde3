#!/usr/bin/env python3
"""
Whisper Integration for Voice Input
Handles audio transcription for conversational interface
"""

import os
import io
import logging
from typing import Optional, Union
import tempfile
from pathlib import Path
import openai
from openai import OpenAI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WhisperTranscription:
    """
    Whisper API integration for audio transcription
    """
    
    def __init__(self, api_key: Optional[str] = None, model: str = "whisper-1"):
        """
        Initialize Whisper transcription
        
        Args:
            api_key: OpenAI API key (or set OPENAI_API_KEY env var)
            model: Whisper model to use
        """
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key required for Whisper. Set OPENAI_API_KEY environment variable.")
        
        self.client = OpenAI(api_key=self.api_key)
        self.model = model
        
        # Supported audio formats
        self.supported_formats = ['.mp3', '.mp4', '.mpeg', '.mpga', '.m4a', '.wav', '.webm']
        
    async def transcribe_audio(self, 
                              audio_data: Union[bytes, str, Path],
                              language: Optional[str] = None,
                              prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        Transcribe audio to text using Whisper
        
        Args:
            audio_data: Audio file path, bytes, or file-like object
            language: Language code (e.g., 'en', 'id') - auto-detect if None
            prompt: Optional prompt to guide transcription
            
        Returns:
            Dict with transcription results
        """
        try:
            # Handle different input types
            if isinstance(audio_data, (str, Path)):
                # File path
                audio_file_path = Path(audio_data)
                if not audio_file_path.exists():
                    raise FileNotFoundError(f"Audio file not found: {audio_file_path}")
                
                with open(audio_file_path, 'rb') as audio_file:
                    transcript = await self._transcribe_file(audio_file, language, prompt)
                    
            elif isinstance(audio_data, bytes):
                # Bytes data - save to temporary file
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                    temp_file.write(audio_data)
                    temp_file.flush()
                    
                    with open(temp_file.name, 'rb') as audio_file:
                        transcript = await self._transcribe_file(audio_file, language, prompt)
                    
                    # Clean up temp file
                    os.unlink(temp_file.name)
            
            else:
                raise ValueError("audio_data must be file path, bytes, or file-like object")
            
            return {
                "success": True,
                "text": transcript.text,
                "language": getattr(transcript, 'language', None),
                "duration": getattr(transcript, 'duration', None),
                "confidence": getattr(transcript, 'confidence', None)
            }
            
        except Exception as e:
            logger.error(f"Whisper transcription error: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "text": "",
                "language": None
            }
    
    async def _transcribe_file(self, audio_file, language: Optional[str], prompt: Optional[str]):
        """Internal method to transcribe audio file"""
        kwargs = {
            "model": self.model,
            "file": audio_file
        }
        
        if language:
            kwargs["language"] = language
        
        if prompt:
            kwargs["prompt"] = prompt
        
        return self.client.audio.transcriptions.create(**kwargs)
    
    def validate_audio_format(self, file_path: Union[str, Path]) -> bool:
        """
        Validate if audio file format is supported
        
        Args:
            file_path: Path to audio file
            
        Returns:
            True if format is supported
        """
        file_path = Path(file_path)
        return file_path.suffix.lower() in self.supported_formats
    
    async def transcribe_with_fallback(self,
                                     audio_data: Union[bytes, str, Path],
                                     fallback_message: str = "Sorry, I couldn't understand the audio. Could you please type your message?") -> str:
        """
        Transcribe audio with fallback message on failure
        
        Args:
            audio_data: Audio data to transcribe
            fallback_message: Message to return if transcription fails
            
        Returns:
            Transcribed text or fallback message
        """
        result = await self.transcribe_audio(audio_data)
        
        if result["success"] and result["text"].strip():
            return result["text"].strip()
        else:
            logger.warning(f"Transcription failed: {result.get('error', 'Unknown error')}")
            return fallback_message

class VoiceInterface:
    """
    Voice interface for property assistant
    Combines Whisper transcription with conversation flow
    """
    
    def __init__(self, whisper_api_key: Optional[str] = None):
        """
        Initialize voice interface
        
        Args:
            whisper_api_key: OpenAI API key for Whisper
        """
        self.whisper = WhisperTranscription(whisper_api_key)
        self.conversation_context = []
        
    async def process_voice_input(self, 
                                audio_data: Union[bytes, str, Path],
                                conversation_assistant) -> Dict[str, Any]:
        """
        Process voice input through complete conversation flow
        
        Args:
            audio_data: Audio data to process
            conversation_assistant: Assistant instance to handle conversation
            
        Returns:
            Dict with transcription and response
        """
        try:
            # Transcribe audio
            transcription_result = await self.whisper.transcribe_audio(audio_data)
            
            if not transcription_result["success"]:
                return {
                    "success": False,
                    "error": "Transcription failed",
                    "transcription": "",
                    "response": "I'm sorry, I couldn't understand your voice message. Could you please try again or type your message?"
                }
            
            transcribed_text = transcription_result["text"]
            
            if not transcribed_text.strip():
                return {
                    "success": False,
                    "error": "Empty transcription",
                    "transcription": "",
                    "response": "I didn't catch that. Could you please speak more clearly or try typing your message?"
                }
            
            # Process through conversation assistant
            response = await conversation_assistant.get_response(transcribed_text)
            
            return {
                "success": True,
                "transcription": transcribed_text,
                "response": response,
                "language": transcription_result.get("language"),
                "confidence": transcription_result.get("confidence")
            }
            
        except Exception as e:
            logger.error(f"Voice processing error: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "transcription": "",
                "response": "I encountered an error processing your voice message. Please try again or type your message."
            }
    
    def get_voice_prompts(self) -> Dict[str, str]:
        """
        Get voice-specific prompts for better transcription
        
        Returns:
            Dict of prompts for different contexts
        """
        return {
            "property_search": "The user is looking for property rentals or purchases in Bali. They might mention locations like Canggu, Seminyak, Ubud, bedrooms, bathrooms, price ranges, villas, apartments.",
            "location": "The user is mentioning a location in Bali such as Canggu, Seminyak, Ubud, Kedungu, Jimbaran, Pandawa, Padonan, Babakan, Uluwatu.",
            "price": "The user is mentioning prices in Indonesian Rupiah (IDR) or US Dollars (USD), possibly with terms like million, thousand, per month, yearly.",
            "amenities": "The user is talking about property amenities like swimming pool, furnished, pet-friendly, garden, parking, air conditioning."
        }
    
    async def transcribe_with_context(self,
                                    audio_data: Union[bytes, str, Path],
                                    context: str = "property_search") -> Dict[str, Any]:
        """
        Transcribe audio with context-specific prompts
        
        Args:
            audio_data: Audio data to transcribe
            context: Context for transcription (property_search, location, price, amenities)
            
        Returns:
            Transcription result with context
        """
        prompts = self.get_voice_prompts()
        prompt = prompts.get(context, prompts["property_search"])
        
        return await self.whisper.transcribe_audio(
            audio_data,
            prompt=prompt
        )

# Utility functions for web interface
def create_audio_upload_endpoint():
    """
    Create FastAPI endpoint for audio upload and transcription
    This would be added to the main API server
    """
    from fastapi import UploadFile, File, HTTPException
    
    async def transcribe_audio_endpoint(audio_file: UploadFile = File(...)):
        """
        API endpoint for audio transcription
        """
        try:
            # Validate file format
            if not audio_file.filename.lower().endswith(('.mp3', '.wav', '.m4a', '.webm')):
                raise HTTPException(status_code=400, detail="Unsupported audio format")
            
            # Read audio data
            audio_data = await audio_file.read()
            
            # Initialize voice interface
            voice_interface = VoiceInterface()
            
            # Transcribe
            result = await voice_interface.whisper.transcribe_audio(audio_data)
            
            if result["success"]:
                return {
                    "success": True,
                    "transcription": result["text"],
                    "language": result.get("language"),
                    "confidence": result.get("confidence")
                }
            else:
                raise HTTPException(status_code=500, detail=f"Transcription failed: {result.get('error')}")
                
        except Exception as e:
            logger.error(f"Audio upload error: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
    
    return transcribe_audio_endpoint

# JavaScript code for web interface (to be added to frontend)
VOICE_INTERFACE_JS = """
// Voice recording functionality for web interface
class VoiceRecorder {
    constructor() {
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.isRecording = false;
    }
    
    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            this.mediaRecorder = new MediaRecorder(stream);
            this.audioChunks = [];
            
            this.mediaRecorder.ondataavailable = (event) => {
                this.audioChunks.push(event.data);
            };
            
            this.mediaRecorder.onstop = () => {
                const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
                this.sendAudioToServer(audioBlob);
            };
            
            this.mediaRecorder.start();
            this.isRecording = true;
            
        } catch (error) {
            console.error('Error starting recording:', error);
            alert('Could not access microphone. Please check permissions.');
        }
    }
    
    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.isRecording = false;
            
            // Stop all tracks
            this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
        }
    }
    
    async sendAudioToServer(audioBlob) {
        const formData = new FormData();
        formData.append('audio_file', audioBlob, 'recording.wav');
        
        try {
            const response = await fetch('/api/transcribe', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Send transcribed text to chat
                this.onTranscriptionComplete(result.transcription);
            } else {
                console.error('Transcription failed:', result.error);
                alert('Could not transcribe audio. Please try again.');
            }
            
        } catch (error) {
            console.error('Error sending audio:', error);
            alert('Error processing audio. Please try again.');
        }
    }
    
    onTranscriptionComplete(text) {
        // This should be implemented to send the transcribed text to the chat
        console.log('Transcribed text:', text);
        // Example: chatInput.value = text; sendMessage();
    }
}
"""
