#!/usr/bin/env python3
"""
Firecrawl Integration Service
Based on PRD: Firecrawl-integratie (Data Ingestie)
"""

import json
import time
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from pathlib import Path

from schemas.property_schema import (
    get_complete_property_schema, 
    get_availability_filter_prompt,
    transform_firecrawl_output
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FirecrawlPropertyService:
    """
    Service for extracting property data using Firecrawl MCP Server
    Only processes available properties as per requirements
    """
    
    def __init__(self, batch_size: int = 3, delay_between_batches: float = 2.0):
        self.batch_size = batch_size
        self.delay_between_batches = delay_between_batches
        self.schema = get_complete_property_schema()
        self.prompt = get_availability_filter_prompt()
        
    def extract_single_property(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Extract data from a single property URL
        Returns None if property is not available
        """
        logger.info(f"Extracting property data from: {url}")
        
        try:
            # This would be called via MCP server in practice:
            # firecrawl_extract_mcp-server-firecrawl
            # For now, we'll simulate the structure
            
            # In actual implementation, this would be:
            # result = firecrawl_extract(urls=[url], prompt=self.prompt, schema=self.schema)
            
            # Placeholder for actual MCP call
            raw_data = self._simulate_firecrawl_call(url)
            
            if not raw_data or raw_data == {}:
                logger.info(f"Property not available or no data extracted: {url}")
                return None
            
            # Transform to internal format
            transformed_data = transform_firecrawl_output(raw_data, url)
            
            if transformed_data is None:
                logger.info(f"Property filtered out (not available): {url}")
                return None
                
            transformed_data['scraped_at'] = datetime.now().isoformat()
            
            logger.info(f"Successfully extracted property: {transformed_data.get('id', 'Unknown ID')}")
            return transformed_data
            
        except Exception as e:
            logger.error(f"Error extracting property from {url}: {str(e)}")
            return None
    
    def extract_batch_properties(self, urls: List[str]) -> List[Dict[str, Any]]:
        """
        Extract data from a batch of property URLs
        Only returns available properties
        """
        logger.info(f"Processing batch of {len(urls)} URLs")
        
        available_properties = []
        
        for url in urls:
            property_data = self.extract_single_property(url)
            if property_data:
                available_properties.append(property_data)
            
            # Small delay to be respectful to the server
            time.sleep(0.5)
        
        logger.info(f"Extracted {len(available_properties)} available properties from {len(urls)} URLs")
        return available_properties
    
    def extract_all_properties(self, urls: List[str]) -> List[Dict[str, Any]]:
        """
        Extract data from all property URLs in batches
        Only returns available properties
        """
        logger.info(f"Starting extraction of {len(urls)} property URLs")
        
        all_properties = []
        total_batches = (len(urls) + self.batch_size - 1) // self.batch_size
        
        for i in range(0, len(urls), self.batch_size):
            batch_num = i // self.batch_size + 1
            batch_urls = urls[i:i + self.batch_size]
            
            logger.info(f"Processing batch {batch_num}/{total_batches}")
            
            batch_properties = self.extract_batch_properties(batch_urls)
            all_properties.extend(batch_properties)
            
            # Delay between batches
            if i + self.batch_size < len(urls):
                logger.info(f"Waiting {self.delay_between_batches}s before next batch...")
                time.sleep(self.delay_between_batches)
        
        logger.info(f"Extraction complete: {len(all_properties)} available properties found")
        return all_properties
    
    def save_properties(self, properties: List[Dict[str, Any]], filename: str = None) -> str:
        """
        Save extracted properties to JSON file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"extracted_properties_{timestamp}.json"
        
        # Ensure output directory exists
        output_dir = Path("data/raw")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        filepath = output_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(properties, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved {len(properties)} properties to {filepath}")
        return str(filepath)
    
    def _simulate_firecrawl_call(self, url: str) -> Dict[str, Any]:
        """
        Simulate Firecrawl MCP call for testing
        In production, this would be replaced with actual MCP server call
        """
        # This is a placeholder - in actual implementation, this would be:
        # return firecrawl_extract_mcp_server_firecrawl(
        #     urls=[url], 
        #     prompt=self.prompt, 
        #     schema=self.schema
        # )
        
        # For now, return empty dict to simulate "not available" properties
        return {}
    
    def get_extraction_stats(self, properties: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate extraction statistics
        """
        if not properties:
            return {"total": 0, "message": "No properties extracted"}
        
        # Location analysis
        locations = {}
        for prop in properties:
            location = prop.get('location', 'Unknown')
            locations[location] = locations.get(location, 0) + 1
        
        # Property type analysis
        prop_types = {}
        for prop in properties:
            prop_type = prop.get('additional_details', {}).get('property_type', 'Unknown')
            prop_types[prop_type] = prop_types.get(prop_type, 0) + 1
        
        # Bedroom analysis
        bedrooms = {}
        for prop in properties:
            bedroom_count = prop.get('bedrooms', 0)
            bedrooms[bedroom_count] = bedrooms.get(bedroom_count, 0) + 1
        
        # Price analysis
        priced_properties = []
        for prop in properties:
            price = prop.get('price', '')
            if price and price != "Price On Request" and price.replace('.', '').replace(',', '').isdigit():
                try:
                    price_num = float(price.replace('.', '').replace(',', ''))
                    priced_properties.append(price_num)
                except:
                    pass
        
        stats = {
            "total_properties": len(properties),
            "locations": dict(sorted(locations.items(), key=lambda x: x[1], reverse=True)),
            "property_types": dict(sorted(prop_types.items(), key=lambda x: x[1], reverse=True)),
            "bedroom_distribution": dict(sorted(bedrooms.items())),
            "price_stats": {
                "total_with_price": len(priced_properties),
                "average_price": sum(priced_properties) / len(priced_properties) if priced_properties else 0,
                "min_price": min(priced_properties) if priced_properties else 0,
                "max_price": max(priced_properties) if priced_properties else 0
            }
        }
        
        return stats

# CLI interface for manual test runs (as per PRD requirement)
def main():
    """
    CLI interface for manual testing
    """
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python firecrawl_service.py <command> [args]")
        print("Commands:")
        print("  test <url>           - Test single property extraction")
        print("  batch <urls_file>    - Process batch from file")
        print("  stats <json_file>    - Show stats for extracted data")
        return
    
    command = sys.argv[1]
    service = FirecrawlPropertyService()
    
    if command == "test" and len(sys.argv) > 2:
        url = sys.argv[2]
        result = service.extract_single_property(url)
        if result:
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print("No data extracted (property not available or error)")
    
    elif command == "stats" and len(sys.argv) > 2:
        json_file = sys.argv[2]
        with open(json_file, 'r', encoding='utf-8') as f:
            properties = json.load(f)
        stats = service.get_extraction_stats(properties)
        print(json.dumps(stats, indent=2, ensure_ascii=False))
    
    else:
        print("Invalid command or missing arguments")

if __name__ == "__main__":
    main()
