#!/usr/bin/env python3
"""
Enhanced Property Assistant
Combines OpenAI, Whisper, Memory, and Slack integrations
"""

import json
import logging
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import asyncio
import uuid

# Local imports
from conversational.openai_integration import OpenAIPropertyAssistant
from conversational.whisper_integration import VoiceInterface
from conversational.memory_persistence import MemoryManager, get_or_create_session
from integrations.slack_integration import SlackIntegration
from api.search_api import PropertySearchRequest

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedPropertyAssistant:
    """
    Enhanced property assistant with full feature integration
    """
    
    def __init__(self, 
                 openai_api_key: Optional[str] = None,
                 slack_webhook_url: Optional[str] = None,
                 api_base_url: str = "http://localhost:8000"):
        """
        Initialize enhanced assistant
        
        Args:
            openai_api_key: OpenAI API key
            slack_webhook_url: Slack webhook URL
            api_base_url: Property search API base URL
        """
        # Initialize components
        self.openai_assistant = OpenAIPropertyAssistant(openai_api_key)
        self.voice_interface = VoiceInterface(openai_api_key)
        self.memory_manager = MemoryManager()
        self.slack_integration = SlackIntegration(slack_webhook_url)
        self.api_base_url = api_base_url
        
        # Current session
        self.current_session_id: Optional[str] = None
        
    async def start_conversation(self, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        """
        Start new conversation or resume existing one
        
        Args:
            user_id: Optional user identifier
            session_id: Optional existing session ID
            
        Returns:
            Session ID
        """
        try:
            # Get or create session
            if session_id:
                session = self.memory_manager.get_session(session_id)
                if session:
                    self.current_session_id = session_id
                    # Restore conversation history to OpenAI assistant
                    self._restore_conversation_history(session.conversation_history)
                    logger.info(f"Resumed session: {session_id}")
                    return session_id
            
            # Create new session
            self.current_session_id = self.memory_manager.create_session(user_id)
            logger.info(f"Started new session: {self.current_session_id}")
            return self.current_session_id
            
        except Exception as e:
            logger.error(f"Error starting conversation: {str(e)}")
            raise
    
    async def process_text_message(self, message: str) -> Dict[str, Any]:
        """
        Process text message through complete conversation flow
        
        Args:
            message: User's text message
            
        Returns:
            Response with assistant reply and metadata
        """
        try:
            if not self.current_session_id:
                self.current_session_id = await self.start_conversation()
            
            # Add user message to memory
            self.memory_manager.add_message(
                self.current_session_id,
                "user",
                message
            )
            
            # Get AI response with function calling
            response = await self.openai_assistant.get_response(
                message,
                self._search_properties_callback
            )
            
            # Add assistant response to memory
            self.memory_manager.add_message(
                self.current_session_id,
                "assistant", 
                response
            )
            
            # Check if user seems ready for handoff
            handoff_triggered = await self._check_for_handoff(message, response)
            
            return {
                "success": True,
                "response": response,
                "session_id": self.current_session_id,
                "handoff_triggered": handoff_triggered
            }
            
        except Exception as e:
            logger.error(f"Error processing text message: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "response": "I'm sorry, I encountered an error. Please try again."
            }
    
    async def process_voice_message(self, audio_data: Union[bytes, str, Path]) -> Dict[str, Any]:
        """
        Process voice message through complete conversation flow
        
        Args:
            audio_data: Audio data to process
            
        Returns:
            Response with transcription and assistant reply
        """
        try:
            if not self.current_session_id:
                self.current_session_id = await self.start_conversation()
            
            # Process voice input
            voice_result = await self.voice_interface.process_voice_input(
                audio_data,
                self.openai_assistant
            )
            
            if not voice_result["success"]:
                return voice_result
            
            # Add transcription and response to memory
            self.memory_manager.add_message(
                self.current_session_id,
                "user",
                voice_result["transcription"],
                {"input_type": "voice", "confidence": voice_result.get("confidence")}
            )
            
            self.memory_manager.add_message(
                self.current_session_id,
                "assistant",
                voice_result["response"]
            )
            
            # Check for handoff
            handoff_triggered = await self._check_for_handoff(
                voice_result["transcription"],
                voice_result["response"]
            )
            
            return {
                "success": True,
                "transcription": voice_result["transcription"],
                "response": voice_result["response"],
                "session_id": self.current_session_id,
                "handoff_triggered": handoff_triggered,
                "language": voice_result.get("language"),
                "confidence": voice_result.get("confidence")
            }
            
        except Exception as e:
            logger.error(f"Error processing voice message: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "response": "I'm sorry, I couldn't process your voice message. Please try again."
            }
    
    async def _search_properties_callback(self, **search_params) -> Dict[str, Any]:
        """
        Callback function for property search (used by OpenAI function calling)
        
        Args:
            **search_params: Search parameters from function calling
            
        Returns:
            Search results
        """
        try:
            import requests
            
            # Make API call to search endpoint
            response = requests.post(
                f"{self.api_base_url}/api/search",
                json={**search_params, "limit": 3},
                timeout=30
            )
            
            if response.status_code == 200:
                results = response.json()
                
                # Save search to memory
                if self.current_session_id:
                    self.memory_manager.add_search_result(
                        self.current_session_id,
                        search_params,
                        results
                    )
                    
                    # Update user preferences
                    self.memory_manager.update_preferences(
                        self.current_session_id,
                        search_params
                    )
                
                return results
            else:
                logger.error(f"Search API error: {response.status_code}")
                return {
                    "error": f"Search failed with status {response.status_code}",
                    "total": 0,
                    "properties": []
                }
                
        except Exception as e:
            logger.error(f"Search callback error: {str(e)}")
            return {
                "error": f"Search error: {str(e)}",
                "total": 0,
                "properties": []
            }
    
    async def _check_for_handoff(self, user_message: str, assistant_response: str) -> bool:
        """
        Check if conversation should be handed off to sales team
        
        Args:
            user_message: User's message
            assistant_response: Assistant's response
            
        Returns:
            True if handoff was triggered
        """
        try:
            # Handoff triggers
            handoff_keywords = [
                "contact", "call", "speak", "agent", "human", "person",
                "interested", "book", "visit", "viewing", "schedule",
                "serious", "ready", "proceed", "next step"
            ]
            
            user_lower = user_message.lower()
            response_lower = assistant_response.lower()
            
            # Check if user is requesting contact or showing serious interest
            handoff_triggered = any(keyword in user_lower for keyword in handoff_keywords)
            
            # Also check if assistant offered handoff and user agreed
            if "contact" in response_lower and any(word in user_lower for word in ["yes", "sure", "okay", "please"]):
                handoff_triggered = True
            
            if handoff_triggered and self.current_session_id:
                await self._trigger_sales_handoff()
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking for handoff: {str(e)}")
            return False
    
    async def _trigger_sales_handoff(self):
        """
        Trigger sales handoff by sending summary to Slack
        """
        try:
            if not self.current_session_id:
                return
            
            session = self.memory_manager.get_session(self.current_session_id)
            if not session:
                return
            
            # Get conversation summary
            summary = self.openai_assistant.get_conversation_summary()
            
            # Send to Slack
            success = await self.slack_integration.send_lead_summary(
                summary,
                session.user_preferences,
                self.current_session_id,
                session.contact_info
            )
            
            if success:
                logger.info(f"Sales handoff triggered for session: {self.current_session_id}")
                # Mark session as completed
                self.memory_manager.complete_session(
                    self.current_session_id,
                    "Handed off to sales team"
                )
            else:
                logger.warning(f"Failed to send handoff for session: {self.current_session_id}")
                
        except Exception as e:
            logger.error(f"Error triggering sales handoff: {str(e)}")
    
    def _restore_conversation_history(self, history: List[Dict[str, Any]]):
        """
        Restore conversation history to OpenAI assistant
        
        Args:
            history: Conversation history from memory
        """
        try:
            # Reset OpenAI assistant conversation
            self.openai_assistant.reset_conversation()
            
            # Add messages back (skip system message)
            for msg in history:
                if msg["role"] in ["user", "assistant"]:
                    self.openai_assistant.add_message(
                        msg["role"],
                        msg["content"],
                        msg.get("metadata", {}).get("function_call")
                    )
                    
        except Exception as e:
            logger.error(f"Error restoring conversation history: {str(e)}")
    
    async def set_contact_info(self, contact_info: str) -> bool:
        """
        Set contact information for current session
        
        Args:
            contact_info: User's contact information
            
        Returns:
            True if successful
        """
        try:
            if not self.current_session_id:
                return False
            
            self.memory_manager.set_contact_info(self.current_session_id, contact_info)
            logger.info(f"Contact info set for session: {self.current_session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error setting contact info: {str(e)}")
            return False
    
    async def get_session_summary(self) -> Dict[str, Any]:
        """
        Get summary of current session
        
        Returns:
            Session summary
        """
        try:
            if not self.current_session_id:
                return {}
            
            session = self.memory_manager.get_session(self.current_session_id)
            if not session:
                return {}
            
            return {
                "session_id": session.session_id,
                "created_at": session.created_at.isoformat(),
                "last_activity": session.last_activity.isoformat(),
                "message_count": len(session.conversation_history),
                "search_count": len(session.search_history),
                "user_preferences": session.user_preferences,
                "status": session.status,
                "has_contact_info": bool(session.contact_info)
            }
            
        except Exception as e:
            logger.error(f"Error getting session summary: {str(e)}")
            return {}
    
    async def end_conversation(self, notes: Optional[str] = None):
        """
        End current conversation
        
        Args:
            notes: Optional notes about the conversation
        """
        try:
            if self.current_session_id:
                self.memory_manager.complete_session(self.current_session_id, notes)
                logger.info(f"Conversation ended: {self.current_session_id}")
                self.current_session_id = None
                
        except Exception as e:
            logger.error(f"Error ending conversation: {str(e)}")

# Convenience function for easy initialization
def create_enhanced_assistant(openai_api_key: str, slack_webhook_url: str = None) -> EnhancedPropertyAssistant:
    """
    Create enhanced assistant with all integrations
    
    Args:
        openai_api_key: OpenAI API key
        slack_webhook_url: Optional Slack webhook URL
        
    Returns:
        EnhancedPropertyAssistant instance
    """
    return EnhancedPropertyAssistant(
        openai_api_key=openai_api_key,
        slack_webhook_url=slack_webhook_url
    )
