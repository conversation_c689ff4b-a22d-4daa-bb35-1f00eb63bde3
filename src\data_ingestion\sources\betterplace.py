#!/usr/bin/env python3
"""
BetterPlace Source Configuration
Specific configuration for betterplace.cc
"""

from typing import List, Dict, Any
from dataclasses import dataclass

@dataclass
class SourceConfig:
    name: str
    base_url: str
    listing_pages: List[str]
    custom_prompt: str
    max_depth: int = 1
    follow_links: bool = True

class BetterPlaceSource:
    """
    Configuration for BetterPlace website
    """
    
    @staticmethod
    def get_config() -> SourceConfig:
        return SourceConfig(
            name="betterplace",
            base_url="https://betterplace.cc",
            listing_pages=[
                "https://betterplace.cc/search-results?ordering=-listing_live_date&ownershipType[0]=Leasehold&ownershipType[1]=Yearly%20Rent&page=1&page_size=50",
                "https://betterplace.cc/search-results?ordering=-listing_live_date&ownershipType[0]=Freehold&page=1&page_size=50"
            ],
            custom_prompt="""
            Extract ALL comprehensive property details from this BetterPlace property page including:
            
            BASIC INFO:
            - Title, price (in IDR), ownership type (Leasehold/Freehold/Yearly Rent), location
            - Bedrooms, bathrooms, description
            
            PROPERTY DETAILS:
            - Property type, property ID (BPVL/BPHL codes), status
            - Year built, lease duration (for leasehold), lease expiry
            - Furnishing level, construction status (Completed/Off-plan/Under Construction)
            
            SIZE & MEASUREMENTS:
            - Land size (sqm), building size (sqm)
            - Price per sqm, levels/floors
            
            LOCATION & PROXIMITY:
            - Specific location/area, distance to beach
            - Surrounding area type
            
            FEATURES & AMENITIES:
            - Pool type (Private/Shared), living room type (Enclosed/Open)
            - Parking type, specific features mentioned
            - All amenities and features listed
            
            INVESTMENT INFO:
            - ROI information if available
            - Handover date for off-plan properties
            - Investment potential details
            
            MEDIA:
            - ALL image URLs from the property page
            - Virtual tour links if available
            - Floor plan links if available
            
            CONTACT INFO:
            - Listing agent name and contact details
            - Property management company
            
            Extract all available information comprehensively. Focus on investment properties, leasehold/freehold distinctions, and ROI data.
            """,
            max_depth=1,
            follow_links=True
        )
    
    @staticmethod
    def get_sample_property_urls() -> List[str]:
        """
        Sample property URLs for testing BetterPlace extraction
        """
        return [
            "https://betterplace.cc/buy/properties/BPVL02232/",  # 3BR Villa Kedungu
            "https://betterplace.cc/buy/properties/BPHL02233/",  # Hotel/Villa Complex
            "https://betterplace.cc/buy/properties/BPVF02234/",  # 2BR Yearly Rental
            "https://betterplace.cc/buy/properties/BPVL02236/",  # 1BR Townhouse
            "https://betterplace.cc/buy/properties/BPVL02238/",  # 2BR Seminyak Villa
        ]
    
    @staticmethod
    def discover_property_urls(search_results_url: str) -> List[str]:
        """
        Extract property URLs from search results page
        This would be used with firecrawl_map or firecrawl_crawl
        """
        # Pattern for BetterPlace property URLs
        # https://betterplace.cc/buy/properties/BPVL02232/
        # https://betterplace.cc/buy/properties/BPHL02233/
        # https://betterplace.cc/buy/properties/BPVF02234/
        
        # This would be implemented with actual crawling
        # For now, return sample URLs
        return BetterPlaceSource.get_sample_property_urls()
    
    @staticmethod
    def get_betterplace_schema() -> Dict[str, Any]:
        """
        BetterPlace-specific property schema - COMPLETE VERSION
        Includes ALL fields available on BetterPlace property pages
        """
        return {
            "type": "object",
            "properties": {
                # Basic Info
                "title": {"type": "string"},
                "price_idr": {"type": "string"},
                "price_usd": {"type": "string"},
                "price_aud": {"type": "string"},
                "price_eur": {"type": "string"},
                "price_sgd": {"type": "string"},
                "ownership_type": {"type": "string"},  # Leasehold, Freehold, Yearly Rent
                "location": {"type": "string"},
                "bedrooms": {"type": "integer"},
                "bathrooms": {"type": "integer"},
                "description": {"type": "string"},

                # Property Details
                "property_type": {"type": "string"},
                "property_id": {"type": "string"},  # BPVL, BPHL, BPVF codes
                "status": {"type": "string"},
                "year_built": {"type": "integer"},
                "lease_duration": {"type": "string"},  # e.g., "26 Years"
                "lease_expiry": {"type": "string"},    # e.g., "Q3 2051"
                "furnishing": {"type": "string"},
                "construction_status": {"type": "string"},  # Completed, Off-plan, etc.
                "managed_by": {"type": "string"},

                # Size & Measurements
                "land_size_sqm": {"type": "integer"},
                "building_size_sqm": {"type": "integer"},
                "price_per_sqm": {"type": "string"},
                "levels": {"type": "integer"},

                # Location & Proximity
                "distance_to_beach": {"type": "string"},
                "surrounding_area": {"type": "string"},
                "location_map_details": {"type": "string"},

                # Features & Amenities
                "pool_type": {"type": "string"},      # Private, Shared
                "living_room_type": {"type": "string"}, # Enclosed, Open
                "parking_type": {"type": "string"},   # Private Bike, Garage, Open
                "amenities": {"type": "array", "items": {"type": "string"}},
                "all_features": {"type": "array", "items": {"type": "string"}},

                # Investment Info
                "roi_available": {"type": "boolean"},
                "roi_info": {"type": "string"},
                "handover_date": {"type": "string"},
                "investment_potential": {"type": "string"},
                "currency_options": {"type": "array", "items": {"type": "string"}},

                # Media - COMPLETE
                "total_photos": {"type": "integer"},
                "all_images": {"type": "array", "items": {"type": "string"}},
                "video_tour_url": {"type": "string"},
                "tour_360_url": {"type": "string"},
                "floor_plan_url": {"type": "string"},

                # Contact Info - COMPLETE
                "listing_agent": {"type": "string"},
                "agent_whatsapp": {"type": "string"},
                "agent_email": {"type": "string"},
                "management_company": {"type": "string"},
                "company_phone": {"type": "string"},
                "company_email": {"type": "string"},
                "company_address": {"type": "string"},

                # Navigation & Structure
                "breadcrumb": {"type": "array", "items": {"type": "string"}},
                "similar_properties": {"type": "array", "items": {"type": "string"}},
                "tabs_available": {"type": "array", "items": {"type": "string"}},

                # Meta
                "detail_url": {"type": "string"},
                "source": {"type": "string", "default": "betterplace"}
            },
            "required": [
                "title",
                "price_idr",
                "ownership_type",
                "location",
                "bedrooms",
                "bathrooms",
                "property_id",
                "detail_url"
            ]
        }
    
    @staticmethod
    def get_custom_extraction_rules() -> Dict[str, Any]:
        """
        Custom extraction rules specific to BetterPlace
        """
        return {
            "price_patterns": [
                r"IDR\s*([\d,\.]+)",
                r"USD\s*([\d,\.]+)",
                r"([\d,\.]+)\s*IDR"
            ],
            "property_id_patterns": [
                r"(BPVL\d+)",  # Villa Leasehold
                r"(BPHL\d+)",  # Hotel/Villa Complex Leasehold  
                r"(BPVF\d+)",  # Villa Freehold
                r"(BPAP\d+)",  # Apartment
                r"(BPLD\d+)"   # Land
            ],
            "ownership_types": [
                "Leasehold",
                "Freehold", 
                "Yearly Rent",
                "Monthly Rent"
            ],
            "construction_status": [
                "Completed",
                "Off-plan",
                "Under Construction"
            ],
            "image_selectors": [
                "img[src*='betterplace.sgp1.cdn.digitaloceanspaces.com']",
                "img[src*='processed']",
                ".property-image img",
                ".gallery img"
            ]
        }

# Export for use in main ingestion service
def get_source_config():
    return BetterPlaceSource.get_config()

def get_property_urls():
    return BetterPlaceSource.get_sample_property_urls()

def get_betterplace_schema():
    return BetterPlaceSource.get_betterplace_schema()
