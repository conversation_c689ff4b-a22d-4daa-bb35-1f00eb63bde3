#!/usr/bin/env python3
"""
Chat Interface for Property Assistant
Simple CLI-based chat for testing and demo
Based on PRD: Conversational Layer & UX
"""

import json
import sys
import os
from typing import List, Dict, Any
import time

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from conversational.assistant import PropertyAssistant

class ChatInterface:
    """
    Simple chat interface for property assistant
    Implements conversation flow from PRD
    """
    
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        self.assistant = PropertyAssistant(api_base_url)
        self.conversation_history: List[Dict[str, str]] = []
        self.session_active = True
        
    def add_to_history(self, role: str, message: str):
        """Add message to conversation history"""
        self.conversation_history.append({
            "role": role,
            "message": message,
            "timestamp": time.time()
        })
    
    def display_welcome(self):
        """Display welcome message"""
        welcome_msg = """
🏡 Welcome to Bali Property Assistant! 🏡

I'm here to help you find the perfect property in Bali. I can help you search through:
• Budget rentals (IDR 15M-150M/month) 
• Premium furnished rentals (USD 1.5K-10K/month)
• Investment properties (USD 120K-2M+)

Popular locations include Canggu, Seminyak, Ubud, Kedungu, and Jimbaran.

What kind of property are you looking for? You can tell me about:
- Location preferences
- Number of bedrooms/bathrooms  
- Budget range
- Property type (Villa, Apartment, House)
- Special requirements (pool, pet-friendly, furnished)

Let's find your perfect place! 🌴
"""
        print(welcome_msg)
        self.add_to_history("assistant", welcome_msg)
    
    def get_user_input(self) -> str:
        """Get user input with prompt"""
        try:
            user_input = input("\n💬 You: ").strip()
            if user_input.lower() in ['quit', 'exit', 'bye', 'goodbye']:
                self.session_active = False
                return ""
            return user_input
        except KeyboardInterrupt:
            self.session_active = False
            return ""
    
    def process_user_message(self, message: str) -> str:
        """
        Process user message and generate response
        This is where we would integrate with OpenAI API in production
        For demo, we'll simulate the conversation flow
        """
        if not message:
            return ""
        
        self.add_to_history("user", message)
        
        # Simulate AI processing with function calling
        response = self.simulate_ai_response(message)
        
        self.add_to_history("assistant", response)
        return response
    
    def simulate_ai_response(self, user_message: str) -> str:
        """
        Simulate AI assistant response with function calling
        In production, this would be handled by OpenAI API
        """
        message_lower = user_message.lower()
        
        # Check if user is providing search criteria
        search_params = self.extract_search_params(user_message)
        
        # If we have enough info, perform search
        if self.has_sufficient_search_criteria(search_params):
            # Perform search using function calling
            search_results = self.assistant.search_properties(**search_params)
            return self.assistant.format_search_results(search_results)
        
        # Otherwise, ask clarifying questions based on conversation phase
        return self.generate_clarifying_response(user_message, search_params)
    
    def extract_search_params(self, message: str) -> Dict[str, Any]:
        """
        Extract search parameters from user message
        Simple keyword-based extraction for demo
        """
        params = {}
        message_lower = message.lower()
        
        # Location extraction
        locations = ["canggu", "seminyak", "ubud", "kedungu", "jimbaran", "pandawa", "padonan", "babakan", "uluwatu"]
        for location in locations:
            if location in message_lower:
                params['location'] = location.title()
                break
        
        # Bedroom extraction
        if "bedroom" in message_lower or "bed" in message_lower:
            for i in range(1, 11):
                if f"{i} bedroom" in message_lower or f"{i} bed" in message_lower:
                    params['bedrooms'] = i
                    break
        
        # Property type extraction
        if "villa" in message_lower:
            params['property_type'] = "Villa"
        elif "apartment" in message_lower:
            params['property_type'] = "Apartment"
        elif "house" in message_lower:
            params['property_type'] = "House"
        
        # Price extraction (simple)
        if "million" in message_lower or "juta" in message_lower:
            # Extract number before million
            words = message_lower.split()
            for i, word in enumerate(words):
                if "million" in word or "juta" in word:
                    if i > 0:
                        try:
                            price_num = float(words[i-1])
                            params['max_price'] = int(price_num * 1000000)
                        except:
                            pass
        
        # Term extraction
        if "monthly" in message_lower or "month" in message_lower:
            params['term'] = "monthly"
        elif "yearly" in message_lower or "year" in message_lower:
            params['term'] = "yearly"
        elif "buy" in message_lower or "purchase" in message_lower:
            params['term'] = "purchase"
        
        # Amenities
        if "pool" in message_lower:
            params['pool'] = True
        if "pet" in message_lower and ("friendly" in message_lower or "allowed" in message_lower):
            params['pet_friendly'] = True
        if "furnished" in message_lower:
            params['furnished'] = True
        
        return params
    
    def has_sufficient_search_criteria(self, params: Dict[str, Any]) -> bool:
        """
        Check if we have enough criteria to perform a meaningful search
        """
        # Need at least location OR property type OR price range
        return bool(params.get('location') or params.get('property_type') or params.get('max_price'))
    
    def generate_clarifying_response(self, user_message: str, extracted_params: Dict[str, Any]) -> str:
        """
        Generate clarifying questions based on what we know
        """
        state = self.assistant.conversation_state
        
        # Update state with extracted params
        for key, value in extracted_params.items():
            if hasattr(state, key):
                setattr(state, key, value)
        
        # Generate response based on what's missing
        if not state.location:
            return """I'd love to help you find a property! Which area of Bali are you interested in? 

Popular areas include:
🏄‍♂️ Canggu - Great for surfers and digital nomads
🏖️ Seminyak - Beach clubs and nightlife
🌿 Ubud - Rice fields and wellness retreats  
🏔️ Kedungu - Emerging area with great views
🐠 Jimbaran - Family-friendly with seafood restaurants

Where would you like to be located?"""
        
        elif not state.bedrooms:
            return f"""Great choice on {state.location}! How many bedrooms do you need?

I can help you find anything from cozy 1-bedroom apartments to spacious 5+ bedroom villas."""
        
        elif not state.max_price and not state.term:
            return """What's your budget and rental term preference?

For rentals:
💰 Budget: IDR 15-50M per month
💎 Premium: USD 1.5K-10K per month

For purchases:
🏡 Investment: USD 120K-2M+

Are you looking to rent monthly, yearly, or purchase?"""
        
        else:
            # We have basic info, summarize and search
            summary = self.assistant.get_conversation_summary()
            if "enough information" in summary:
                return summary
            else:
                # Perform search with what we have
                search_results = self.assistant.search_properties(**extracted_params)
                return self.assistant.format_search_results(search_results)
    
    def display_quick_replies(self):
        """Display quick reply options"""
        print("\n🔧 Quick options:")
        print("• Type 'more' to see more results")
        print("• Type 'refine' to adjust your search")
        print("• Type 'new search' to start over")
        print("• Type 'quit' to exit")
    
    def run_chat(self):
        """
        Main chat loop
        Implements the conversation flow from PRD
        """
        print("🚀 Starting Bali Property Assistant...")
        
        # Check API connection
        try:
            import requests
            response = requests.get(f"{self.assistant.api_base_url}/", timeout=5)
            if response.status_code != 200:
                print(f"⚠️ Warning: API not responding at {self.assistant.api_base_url}")
                print("Running in demo mode with simulated responses.")
        except:
            print(f"⚠️ Warning: Cannot connect to API at {self.assistant.api_base_url}")
            print("Running in demo mode with simulated responses.")
        
        self.display_welcome()
        
        while self.session_active:
            user_input = self.get_user_input()
            
            if not user_input:
                break
            
            if user_input.lower() == "new search":
                self.assistant.reset_conversation()
                print("\n🔄 Starting a new search...")
                continue
            
            if user_input.lower() == "more":
                print("\n📋 Showing more results would be implemented here...")
                continue
            
            if user_input.lower() == "refine":
                print("\n🔧 What would you like to change about your search criteria?")
                continue
            
            # Process the message
            response = self.process_user_message(user_input)
            
            if response:
                print(f"\n🤖 Assistant: {response}")
                
                # Show quick replies after search results
                if "properties matching" in response.lower():
                    self.display_quick_replies()
        
        print("\n👋 Thank you for using Bali Property Assistant! Have a great day!")
    
    def save_conversation_history(self, filename: str = "conversation_history.json"):
        """Save conversation history to file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.conversation_history, f, indent=2, ensure_ascii=False)
            print(f"💾 Conversation saved to {filename}")
        except Exception as e:
            print(f"❌ Error saving conversation: {str(e)}")

def main():
    """Run the chat interface"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Bali Property Assistant Chat Interface')
    parser.add_argument('--api-url', default='http://localhost:8000', help='API base URL')
    parser.add_argument('--save-history', action='store_true', help='Save conversation history')
    
    args = parser.parse_args()
    
    chat = ChatInterface(args.api_url)
    
    try:
        chat.run_chat()
    finally:
        if args.save_history:
            chat.save_conversation_history()

if __name__ == "__main__":
    main()
