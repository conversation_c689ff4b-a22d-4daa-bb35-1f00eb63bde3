#!/usr/bin/env python3
"""
BaliVillaRealty Source Configuration
Specific configuration for balivillarealty.com
"""

from typing import List, Dict, Any
from dataclasses import dataclass

@dataclass
class SourceConfig:
    name: str
    base_url: str
    listing_pages: List[str]
    custom_prompt: str
    max_depth: int = 1
    follow_links: bool = True

class BaliVillaRealtySource:
    """
    Configuration for BaliVillaRealty website
    """
    
    @staticmethod
    def get_config() -> SourceConfig:
        return SourceConfig(
            name="bali_villa_realty",
            base_url="https://balivillarealty.com",
            listing_pages=[
                "https://balivillarealty.com/bali-villa-rentals/",
                "https://balivillarealty.com/status/for-rent/",
                "https://balivillarealty.com/label/monthly-rental/",
                "https://balivillarealty.com/label/yearly-rental/"
            ],
            custom_prompt="""
            Extract ALL comprehensive property details from this BaliVillaRealty property page including:
            
            BASIC INFO:
            - Title, price (USD and IDR), location, bedrooms, bathrooms, description
            - Property type, property ID (IPB codes), status
            
            RENTAL DETAILS:
            - Rent status (Monthly/Yearly rental), rent availability
            - Price per month and per year in USD and IDR
            - Furniture status (Fully Furnished, etc.)
            
            SIZE & MEASUREMENTS:
            - Property size (sqm), land area (sqm)
            - Pool size if mentioned
            
            LOCATION & ADDRESS:
            - Specific location/area, city, state/county
            - Full address details
            - Map coordinates if available
            
            FEATURES & AMENITIES:
            - ALL features listed (Air Conditioning, Swimming Pool, Kitchen set, etc.)
            - Detailed amenities and equipment
            - Furniture and appliances included
            
            MEDIA:
            - ALL image URLs from the property page
            - Total number of photos available
            - Gallery images and thumbnails
            
            CONTACT & COMPANY INFO:
            - Company name (Bali Villa Realty / Ilot Property Bali)
            - Contact phone numbers, email, address
            - WhatsApp contact information
            
            SOCIAL & SHARING:
            - Social media sharing options
            - Available social platforms
            
            CURRENCY & PRICING:
            - Multi-currency support (USD, AUD, EUR, IDR)
            - Price conversions available
            - Rental terms and conditions
            
            SIMILAR PROPERTIES:
            - Related/similar property listings
            - Cross-references to other properties
            
            Extract all available information comprehensively. Focus on rental properties, pricing in multiple currencies, and detailed amenity lists.
            """,
            max_depth=1,
            follow_links=True
        )
    
    @staticmethod
    def get_sample_property_urls() -> List[str]:
        """
        Sample property URLs for testing BaliVillaRealty extraction
        """
        return [
            "https://balivillarealty.com/property/cozy-villa-2-bedrooms-for-rental-in-canggu-bali/",
            "https://balivillarealty.com/property/beautiful-brand-new-2-bedrooms-villa-for-rental-in-padonan/",
            "https://balivillarealty.com/property/charming-2-bedroom-villa-for-rental-in-padonan/",
            "https://balivillarealty.com/property/stunning-3-bedrooms-villa-for-rental-in-umalas-bali-2/",
            "https://balivillarealty.com/property/charming-2-bedrooms-villa-for-rental-in-babakan-canggu-bali/"
        ]
    
    @staticmethod
    def get_bali_villa_realty_schema() -> Dict[str, Any]:
        """
        BaliVillaRealty-specific property schema
        Extends the base schema with BaliVillaRealty-specific fields
        """
        return {
            "type": "object",
            "properties": {
                # Basic Info
                "title": {"type": "string"},
                "price_usd": {"type": "string"},
                "price_idr": {"type": "string"},
                "price_monthly_usd": {"type": "string"},
                "price_monthly_idr": {"type": "string"},
                "price_yearly_usd": {"type": "string"},
                "price_yearly_idr": {"type": "string"},
                "location": {"type": "string"},
                "bedrooms": {"type": "integer"},
                "bathrooms": {"type": "integer"},
                "description": {"type": "string"},
                
                # Property Details
                "property_type": {"type": "string"},
                "property_id": {"type": "string"},  # IPB codes
                "property_status": {"type": "string"},
                "rent_status": {"type": "array", "items": {"type": "string"}},  # Monthly/Yearly
                "rent_availability": {"type": "string"},
                "furniture_status": {"type": "string"},
                
                # Size & Measurements
                "property_size_sqm": {"type": "integer"},
                "land_area_sqm": {"type": "integer"},
                "pool_size": {"type": "string"},
                
                # Location & Address
                "area": {"type": "string"},
                "city": {"type": "string"},
                "state_county": {"type": "string"},
                "full_address": {"type": "string"},
                "map_coordinates": {"type": "string"},
                
                # Features & Amenities
                "features": {"type": "array", "items": {"type": "string"}},
                "amenities": {"type": "array", "items": {"type": "string"}},
                "furniture_included": {"type": "array", "items": {"type": "string"}},
                "appliances": {"type": "array", "items": {"type": "string"}},
                
                # Media
                "images": {"type": "array", "items": {"type": "string"}},
                "total_photos": {"type": "integer"},
                "gallery_images": {"type": "array", "items": {"type": "string"}},
                
                # Contact & Company
                "company_name": {"type": "string"},
                "contact_phone": {"type": "string"},
                "contact_email": {"type": "string"},
                "company_address": {"type": "string"},
                "whatsapp_contact": {"type": "string"},
                
                # Social & Sharing
                "social_sharing": {"type": "array", "items": {"type": "string"}},
                "social_platforms": {"type": "array", "items": {"type": "string"}},
                
                # Currency & Pricing
                "currency_options": {"type": "array", "items": {"type": "string"}},
                "price_conversions": {"type": "object"},
                "rental_terms": {"type": "string"},
                
                # Similar Properties
                "similar_properties": {"type": "array", "items": {"type": "string"}},
                "related_listings": {"type": "array", "items": {"type": "string"}},
                
                # Meta
                "detail_url": {"type": "string"},
                "source": {"type": "string", "default": "bali_villa_realty"}
            },
            "required": [
                "title", 
                "price_usd", 
                "location", 
                "bedrooms", 
                "bathrooms", 
                "property_id", 
                "detail_url"
            ]
        }
    
    @staticmethod
    def get_custom_extraction_rules() -> Dict[str, Any]:
        """
        Custom extraction rules specific to BaliVillaRealty
        """
        return {
            "price_patterns": [
                r"\$([0-9,]+)",  # USD prices
                r"([0-9,]+)\s*USD",
                r"([0-9,]+)\s*IDR",
                r"IDR\s*([0-9,]+)"
            ],
            "property_id_patterns": [
                r"(IPB\d+)",  # IPB property codes
                r"Property ID\s*(IPB\d+)"
            ],
            "rental_types": [
                "Monthly Rental",
                "Yearly Rental",
                "Long-term Rental"
            ],
            "furniture_status": [
                "Fully Furnished",
                "Semi Furnished", 
                "Unfurnished"
            ],
            "currency_codes": [
                "USD", "AUD", "EUR", "IDR"
            ],
            "image_selectors": [
                "img[src*='balivillarealty.com/wp-content/uploads']",
                ".property-gallery img",
                ".gallery img"
            ],
            "feature_categories": [
                "Air Conditioning", "Swimming Pool", "Kitchen set",
                "Refrigerator", "TV", "Sofa", "Bed", "Fan",
                "Stove", "Water Dispenser"
            ]
        }
    
    @staticmethod
    def get_location_mapping() -> Dict[str, str]:
        """
        Location mapping for BaliVillaRealty areas
        """
        return {
            "Canggu": "Canggu - Babakan - Batu Bolong - Berawa - Padonan - Pererenan - Tibubeneng - Tumbak Bayuh",
            "Seminyak": "Seminyak - Batu Belig - Legian - Petitenget",
            "Uluwatu": "Uluwatu - Balangan - Bingin - Nunggalan - Pandawa - Pecatu",
            "Ungasan": "Ungasan - Jimbaran",
            "Ubud": "Ubud - Pejeng - Tegallalang",
            "Kerobokan": "Kerobokan",
            "Umalas": "Umalas",
            "Sanur": "East Coast - Nusa Penida - Sanur",
            "Nusa Dua": "Nusa Dua",
            "West Coast": "West Coast - Cepaka - Kaba-Kaba - Kediri - Kedungu - Nyanyi - Pasut - Tanah Lot"
        }

# Export for use in main ingestion service
def get_source_config():
    return BaliVillaRealtySource.get_config()

def get_property_urls():
    return BaliVillaRealtySource.get_sample_property_urls()

def get_bali_villa_realty_schema():
    return BaliVillaRealtySource.get_bali_villa_realty_schema()
