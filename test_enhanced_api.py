#!/usr/bin/env python3
"""
Test script for the Enhanced API server with OpenAI
"""

import requests
import json
import time

API_BASE_URL = "http://localhost:8000"

def test_enhanced_api_health():
    """Test enhanced API health check"""
    print("🔍 Testing Enhanced API Health...")
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Enhanced API is running!")
            print(f"   Status: {data['status']}")
            print(f"   AI Enabled: {data['ai_enabled']}")
            print(f"   Total Properties: {data['total_properties']}")
            print(f"   Version: {data['version']}")
            return True
        else:
            print(f"❌ API returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API connection failed: {str(e)}")
        return False

def test_openai_chat():
    """Test OpenAI GPT-4 chat with function calling"""
    print("\n🤖 Testing OpenAI GPT-4 Chat...")
    try:
        chat_data = {
            "message": "I need a 2 bedroom villa in Canggu with a swimming pool"
        }
        
        print(f"   Sending: {chat_data['message']}")
        print("   🧠 Waiting for GPT-4 response...")
        
        response = requests.post(
            f"{API_BASE_URL}/api/chat",
            json=chat_data,
            timeout=30  # OpenAI can take a while
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ OpenAI Chat successful!")
            print(f"   Success: {data['success']}")
            print(f"   Session ID: {data['session_id']}")
            print(f"   Handoff Triggered: {data['handoff_triggered']}")
            print(f"   Response Preview: {data['response'][:200]}...")
            
            # Check if it contains property information
            if "property" in data['response'].lower() or "villa" in data['response'].lower():
                print("   🏠 Response contains property information!")
            
            return True
        else:
            print(f"❌ Chat failed with status: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ OpenAI Chat test failed: {str(e)}")
        return False

def test_conversation_flow():
    """Test multi-turn conversation"""
    print("\n💬 Testing Conversation Flow...")
    try:
        session_id = None
        
        # First message
        chat_data = {
            "message": "Hi, I'm looking for a property in Bali"
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/chat",
            json=chat_data,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            session_id = data['session_id']
            print(f"✅ First message successful!")
            print(f"   Session ID: {session_id}")
            print(f"   Response: {data['response'][:100]}...")
        else:
            print(f"❌ First message failed")
            return False
        
        # Second message with session
        chat_data = {
            "message": "I want a 2 bedroom villa in Seminyak with pool",
            "session_id": session_id
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/chat",
            json=chat_data,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Second message successful!")
            print(f"   Same Session: {data['session_id'] == session_id}")
            print(f"   Response: {data['response'][:100]}...")
            return True
        else:
            print(f"❌ Second message failed")
            return False
            
    except Exception as e:
        print(f"❌ Conversation flow test failed: {str(e)}")
        return False

def test_function_calling():
    """Test OpenAI function calling for property search"""
    print("\n🔧 Testing Function Calling...")
    try:
        chat_data = {
            "message": "Show me 3 bedroom villas in Seminyak with swimming pool under 5000 USD"
        }
        
        print(f"   Query: {chat_data['message']}")
        print("   🔍 Expecting function call to search_properties...")
        
        response = requests.post(
            f"{API_BASE_URL}/api/chat",
            json=chat_data,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Function calling test successful!")
            
            # Check if response contains specific property details
            response_text = data['response'].lower()
            if "seminyak" in response_text and "villa" in response_text:
                print("   🎯 Function calling worked - found Seminyak villa!")
            
            if "swimming pool" in response_text or "pool" in response_text:
                print("   🏊 Pool requirement processed!")
            
            if "3 bedroom" in response_text or "bedrooms" in response_text:
                print("   🛏️ Bedroom requirement processed!")
            
            return True
        else:
            print(f"❌ Function calling failed with status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Function calling test failed: {str(e)}")
        return False

def test_enhanced_stats():
    """Test enhanced stats endpoint"""
    print("\n📊 Testing Enhanced Stats...")
    try:
        response = requests.get(f"{API_BASE_URL}/api/stats", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Enhanced stats retrieved!")
            print(f"   Total Listings: {data['total_listings']}")
            print(f"   AI Enabled: {data['ai_enabled']}")
            print(f"   Active Sessions: {data['active_sessions']}")
            return True
        else:
            print(f"❌ Stats failed with status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Stats test failed: {str(e)}")
        return False

def run_enhanced_tests():
    """Run all enhanced API tests"""
    print("🤖 TESTING ENHANCED BALI PROPERTY SEARCH API WITH OPENAI")
    print("=" * 60)
    
    # Wait a moment for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    tests = [
        ("Enhanced API Health", test_enhanced_api_health),
        ("OpenAI GPT-4 Chat", test_openai_chat),
        ("Conversation Flow", test_conversation_flow),
        ("Function Calling", test_function_calling),
        ("Enhanced Stats", test_enhanced_stats)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
        if not result:
            print(f"   ⚠️ {test_name} failed - continuing with other tests...")
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 ENHANCED TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{len(tests)} tests passed")
    
    if passed >= 3:  # At least basic functionality working
        print("🎉 Enhanced API is working with OpenAI integration!")
        print("\n🌐 You can now test advanced features:")
        print("   1. Open frontend/index.html in your browser")
        print("   2. Try natural language: 'I need a villa in Canggu with pool'")
        print("   3. GPT-4 will understand and search properties")
        print("   4. API docs: http://localhost:8000/docs")
        
        if passed == len(tests):
            print("   5. 🎤 Voice transcription ready (upload audio files)")
            print("   6. 💬 Multi-turn conversations working")
    else:
        print("⚠️ Some advanced features failed. Basic functionality may still work.")
        print("   Check OpenAI API key and internet connection.")
    
    return passed >= 3

if __name__ == "__main__":
    run_enhanced_tests()
