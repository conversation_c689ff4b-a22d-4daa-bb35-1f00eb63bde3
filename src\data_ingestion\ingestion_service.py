#!/usr/bin/env python3
"""
Main Data Ingestion Service
Orchestrates the complete data ingestion pipeline
Based on PRD: Firecrawl-integratie (Data Ingestie)
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from pathlib import Path
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from database.models import DatabaseManager, Source, Listing, CrawlSession, create_listing_from_extracted_data
from data_ingestion.firecrawl_service import FirecrawlPropertyService
from data_ingestion.sources.bali_home_immo import get_source_config as get_bali_config, get_property_urls as get_bali_urls
from data_ingestion.sources.betterplace import get_source_config as get_betterplace_config, get_property_urls as get_betterplace_urls
from data_ingestion.sources.bali_villa_realty import get_source_config as get_bvr_config, get_property_urls as get_bvr_urls

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PropertyIngestionService:
    """
    Main service for ingesting property data from multiple sources
    Implements the complete data pipeline from PRD
    """
    
    def __init__(self, database_url: str = "sqlite:///data/properties.db"):
        self.db_manager = DatabaseManager(database_url)
        self.firecrawl_service = FirecrawlPropertyService()
        
        # Ensure database exists
        self.db_manager.create_tables()
        
        # Ensure data directories exist
        Path("data/raw").mkdir(parents=True, exist_ok=True)
        Path("data/processed").mkdir(parents=True, exist_ok=True)
        Path("logs").mkdir(parents=True, exist_ok=True)
    
    def setup_source(self, source_name: str) -> int:
        """
        Setup or get source in database
        Returns source_id
        """
        session = self.db_manager.get_session()
        
        try:
            # Check if source exists
            source = session.query(Source).filter(Source.name == source_name).first()

            if not source:
                # Create new source based on configuration
                if source_name == "bali_home_immo":
                    config = get_bali_config()
                    source = Source(
                        name=config.name,
                        base_url=config.base_url,
                        is_active=True
                    )
                    session.add(source)
                    session.commit()
                    logger.info(f"Created new source: {source_name}")
                elif source_name == "betterplace":
                    config = get_betterplace_config()
                    source = Source(
                        name=config.name,
                        base_url=config.base_url,
                        is_active=True
                    )
                    session.add(source)
                    session.commit()
                    logger.info(f"Created new source: {source_name}")
                elif source_name == "bali_villa_realty":
                    config = get_bvr_config()
                    source = Source(
                        name=config.name,
                        base_url=config.base_url,
                        is_active=True
                    )
                    session.add(source)
                    session.commit()
                    logger.info(f"Created new source: {source_name}")
                else:
                    raise ValueError(f"Unknown source: {source_name}")
            
            return source.id
            
        finally:
            self.db_manager.close_session(session)
    
    def start_crawl_session(self, source_id: int) -> int:
        """
        Start a new crawl session
        Returns session_id
        """
        session = self.db_manager.get_session()
        
        try:
            crawl_session = CrawlSession(
                source_id=source_id,
                started_at=datetime.utcnow(),
                status='running'
            )
            session.add(crawl_session)
            session.commit()
            
            logger.info(f"Started crawl session {crawl_session.id} for source {source_id}")
            return crawl_session.id
            
        finally:
            self.db_manager.close_session(session)
    
    def complete_crawl_session(self, session_id: int, stats: Dict[str, Any], error: Optional[str] = None):
        """
        Complete a crawl session with statistics
        """
        session = self.db_manager.get_session()
        
        try:
            crawl_session = session.query(CrawlSession).filter(CrawlSession.id == session_id).first()
            
            if crawl_session:
                crawl_session.completed_at = datetime.utcnow()
                crawl_session.status = 'failed' if error else 'completed'
                crawl_session.total_urls_processed = stats.get('total_urls', 0)
                crawl_session.successful_extractions = stats.get('successful', 0)
                crawl_session.failed_extractions = stats.get('failed', 0)
                crawl_session.available_properties_found = stats.get('available_properties', 0)
                
                if error:
                    crawl_session.error_message = error
                
                session.commit()
                logger.info(f"Completed crawl session {session_id}")
            
        finally:
            self.db_manager.close_session(session)
    
    def save_listings_to_database(self, properties: List[Dict[str, Any]], source_id: int) -> int:
        """
        Save extracted properties to database with duplicate detection
        Returns number of new listings saved
        """
        session = self.db_manager.get_session()
        new_listings_count = 0
        
        try:
            for property_data in properties:
                # Check for duplicates by source_url
                existing = session.query(Listing).filter(
                    Listing.source_url == property_data.get('source_url')
                ).first()
                
                if existing:
                    # Update existing listing
                    logger.info(f"Updating existing listing: {property_data.get('id')}")
                    # Update logic here if needed
                    continue
                
                # Create new listing
                listing = create_listing_from_extracted_data(property_data, source_id)
                session.add(listing)
                new_listings_count += 1
                
                logger.info(f"Added new listing: {property_data.get('id')}")
            
            session.commit()
            logger.info(f"Saved {new_listings_count} new listings to database")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error saving listings to database: {str(e)}")
            raise
            
        finally:
            self.db_manager.close_session(session)
        
        return new_listings_count
    
    def ingest_source(self, source_name: str, limit: Optional[int] = None) -> Dict[str, Any]:
        """
        Complete ingestion pipeline for a source
        
        Args:
            source_name: Name of the source to ingest
            limit: Optional limit on number of URLs to process (for testing)
        
        Returns:
            Dictionary with ingestion statistics
        """
        logger.info(f"Starting ingestion for source: {source_name}")
        
        # Setup source
        source_id = self.setup_source(source_name)
        
        # Start crawl session
        session_id = self.start_crawl_session(source_id)
        
        stats = {
            'source_name': source_name,
            'total_urls': 0,
            'successful': 0,
            'failed': 0,
            'available_properties': 0,
            'new_listings_saved': 0
        }
        
        error_message = None
        
        try:
            # Get URLs for source
            if source_name == "bali_home_immo":
                urls = get_bali_urls()
            elif source_name == "betterplace":
                urls = get_betterplace_urls()
            elif source_name == "bali_villa_realty":
                urls = get_bvr_urls()
            else:
                raise ValueError(f"Unknown source: {source_name}")
            
            # Apply limit if specified
            if limit:
                urls = urls[:limit]
                logger.info(f"Limited to {limit} URLs for testing")
            
            stats['total_urls'] = len(urls)
            
            # Extract properties using Firecrawl
            logger.info(f"Extracting data from {len(urls)} URLs...")
            properties = self.firecrawl_service.extract_all_properties(urls)
            
            stats['available_properties'] = len(properties)
            stats['successful'] = len(properties)
            stats['failed'] = len(urls) - len(properties)
            
            # Save to database
            if properties:
                new_listings = self.save_listings_to_database(properties, source_id)
                stats['new_listings_saved'] = new_listings
                
                # Save raw data to file
                filename = f"{source_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                filepath = self.firecrawl_service.save_properties(properties, filename)
                stats['raw_data_file'] = filepath
            
            logger.info(f"Ingestion completed successfully for {source_name}")
            
        except Exception as e:
            error_message = str(e)
            logger.error(f"Ingestion failed for {source_name}: {error_message}")
            stats['error'] = error_message
            
        finally:
            # Complete crawl session
            self.complete_crawl_session(session_id, stats, error_message)
        
        return stats
    
    def get_available_listings(self, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Get available listings from database with optional filters
        Used by API layer and matching engine
        """
        session = self.db_manager.get_session()
        
        try:
            query = session.query(Listing).filter(
                Listing.is_active == True,
                Listing.status.in_(['Available', 'Online'])
            )
            
            # Apply filters if provided
            if filters:
                if 'location' in filters:
                    locations = filters['location'] if isinstance(filters['location'], list) else [filters['location']]
                    query = query.filter(Listing.location.in_(locations))
                
                if 'max_price' in filters and filters['max_price']:
                    query = query.filter(Listing.price_numeric <= filters['max_price'])
                
                if 'min_bedrooms' in filters and filters['min_bedrooms']:
                    query = query.filter(Listing.bedrooms >= filters['min_bedrooms'])
                
                if 'property_type' in filters:
                    query = query.filter(Listing.property_type == filters['property_type'])
            
            listings = query.all()
            return [listing.to_dict() for listing in listings]
            
        finally:
            self.db_manager.close_session(session)
    
    def get_ingestion_stats(self) -> Dict[str, Any]:
        """
        Get overall ingestion statistics
        """
        session = self.db_manager.get_session()
        
        try:
            total_listings = session.query(Listing).count()
            available_listings = session.query(Listing).filter(
                Listing.is_active == True,
                Listing.status.in_(['Available', 'Online'])
            ).count()
            
            recent_sessions = session.query(CrawlSession).order_by(
                CrawlSession.started_at.desc()
            ).limit(5).all()
            
            return {
                'total_listings': total_listings,
                'available_listings': available_listings,
                'recent_crawl_sessions': [
                    {
                        'id': s.id,
                        'source_id': s.source_id,
                        'started_at': s.started_at.isoformat() if s.started_at else None,
                        'status': s.status,
                        'available_properties_found': s.available_properties_found
                    }
                    for s in recent_sessions
                ]
            }
            
        finally:
            self.db_manager.close_session(session)

# CLI interface for manual operations
def main():
    """
    CLI interface for manual ingestion operations
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='Property Data Ingestion Service')
    parser.add_argument('command', choices=['ingest', 'stats', 'list'], help='Command to execute')
    parser.add_argument('--source', default='bali_home_immo', choices=['bali_home_immo', 'betterplace', 'bali_villa_realty'], help='Source to ingest')
    parser.add_argument('--limit', type=int, help='Limit number of URLs (for testing)')
    parser.add_argument('--database', default='sqlite:///data/properties.db', help='Database URL')
    
    args = parser.parse_args()
    
    service = PropertyIngestionService(args.database)
    
    if args.command == 'ingest':
        stats = service.ingest_source(args.source, args.limit)
        print(f"\nIngestion Results:")
        print(f"Source: {stats['source_name']}")
        print(f"Total URLs: {stats['total_urls']}")
        print(f"Available Properties: {stats['available_properties']}")
        print(f"New Listings Saved: {stats['new_listings_saved']}")
        if 'error' in stats:
            print(f"Error: {stats['error']}")
    
    elif args.command == 'stats':
        stats = service.get_ingestion_stats()
        print(f"\nDatabase Statistics:")
        print(f"Total Listings: {stats['total_listings']}")
        print(f"Available Listings: {stats['available_listings']}")
        print(f"Recent Crawl Sessions: {len(stats['recent_crawl_sessions'])}")
    
    elif args.command == 'list':
        listings = service.get_available_listings()
        print(f"\nAvailable Listings: {len(listings)}")
        for listing in listings[:5]:  # Show first 5
            print(f"- {listing['title'][:50]}... ({listing['location']})")

if __name__ == "__main__":
    main()
