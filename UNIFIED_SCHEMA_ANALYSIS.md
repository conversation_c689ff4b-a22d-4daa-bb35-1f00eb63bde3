# 🔍 Unified Schema Analysis - Complete Field Mapping

## 📊 Field Comparison: Bali Home Immo vs BetterPlace

### ✅ **TRULY UNIFIED FIELDS** (Present in both sources)

| Field | Bali Home Immo | BetterPlace | Unified Type |
|-------|----------------|-------------|--------------|
| `title` | ✅ Property title | ✅ Property title | `string` |
| `location` | ✅ Area/district | ✅ Area/district | `string` |
| `bedrooms` | ✅ Number | ✅ Number | `integer` |
| `bathrooms` | ✅ Number | ✅ Number | `integer` |
| `property_type` | ✅ Villa/House | ✅ Villa/Apartment/Land | `string` |
| `property_id` | ✅ AD019, etc. | ✅ BPVL02232, etc. | `string` |
| `description` | ✅ Full description | ✅ Full description | `string` |
| `images` | ✅ Image URLs array | ✅ All image URLs | `array[string]` |
| `detail_url` | ✅ Source URL | ✅ Source URL | `string` |
| `land_size_sqm` | ✅ Land size | ✅ Land size | `integer` |
| `building_size_sqm` | ✅ Building size | ✅ Building size | `integer` |
| `year_built` | ✅ Construction year | ✅ Construction year | `integer` |
| `furnishing` | ✅ Furnished/Unfurnished | ✅ Fully-Furnished/etc. | `string` |
| `pool_type` | ✅ Private/Shared | ✅ Private/Shared | `string` |
| `parking_type` | ✅ Open/Garage | ✅ Private Bike/Garage | `string` |
| `amenities` | ✅ Features array | ✅ Features array | `array[string]` |

**Total Unified Fields: 16**

### 🏠 **BALI HOME IMMO SPECIFIC FIELDS** (Rental Focus)

| Field | Description | Type |
|-------|-------------|------|
| `payment_term` | monthly/yearly | `string` |
| `status` | Available/Not Available | `string` |
| `availability_date` | When available | `string` |
| `pet_policy` | Pet-friendly/No pets | `string` |
| `sublease_allowed` | Can sublease | `boolean` |
| `monthly_costs` | Cost breakdown object | `object` |
| `indoor_details` | Room details | `object` |
| `outdoor_details` | Garden/pool details | `object` |
| `electricity_capacity` | Watt capacity | `string` |
| `water_source` | PAM/Well | `string` |
| `internet` | Fiber/WiFi type | `string` |
| `air_conditioner_count` | Number of AC units | `integer` |

**Bali Home Immo Unique Fields: 12**

### 🏢 **BETTERPLACE SPECIFIC FIELDS** (Investment Focus)

| Field | Description | Type |
|-------|-------------|------|
| `price_idr` | IDR price | `string` |
| `price_usd` | USD price | `string` |
| `price_aud` | AUD price | `string` |
| `price_eur` | EUR price | `string` |
| `price_sgd` | SGD price | `string` |
| `ownership_type` | Leasehold/Freehold | `string` |
| `lease_duration` | 26 Years, etc. | `string` |
| `lease_expiry` | Q3 2051, etc. | `string` |
| `construction_status` | Completed/Off-plan | `string` |
| `managed_by` | Management company | `string` |
| `price_per_sqm` | Price per square meter | `string` |
| `levels` | Number of floors | `integer` |
| `distance_to_beach` | Minutes to beach | `string` |
| `living_room_type` | Enclosed/Open | `string` |
| `roi_available` | ROI calculation available | `boolean` |
| `roi_info` | ROI details | `string` |
| `handover_date` | For off-plan properties | `string` |
| `investment_potential` | Investment analysis | `string` |
| `currency_options` | Available currencies | `array[string]` |
| `total_photos` | Number of photos | `integer` |
| `video_tour_url` | Video tour link | `string` |
| `tour_360_url` | 360 tour link | `string` |
| `floor_plan_url` | Floor plan link | `string` |
| `location_map_details` | Map information | `string` |
| `agent_whatsapp` | Agent WhatsApp | `string` |
| `agent_email` | Agent email | `string` |
| `company_phone` | Company phone | `string` |
| `company_email` | Company email | `string` |
| `company_address` | Company address | `string` |
| `breadcrumb` | Navigation path | `array[string]` |
| `similar_properties` | Related properties | `array[string]` |
| `tabs_available` | Available tabs | `array[string]` |

**BetterPlace Unique Fields: 31**

## 📈 **FIELD STATISTICS**

```
Total Fields Analyzed: 59
├── Unified Fields: 16 (27%)
├── Bali Home Immo Specific: 12 (20%)
└── BetterPlace Specific: 31 (53%)
```

## 🔧 **RECOMMENDED UNIFIED SCHEMA**

### **Core Unified Fields** (Always present)
```json
{
  "title": "string",
  "location": "string", 
  "bedrooms": "integer",
  "bathrooms": "integer",
  "property_type": "string",
  "property_id": "string",
  "description": "string",
  "images": "array[string]",
  "detail_url": "string",
  "source": "string"
}
```

### **Extended Unified Fields** (Often present)
```json
{
  "land_size_sqm": "integer",
  "building_size_sqm": "integer", 
  "year_built": "integer",
  "furnishing": "string",
  "pool_type": "string",
  "parking_type": "string",
  "amenities": "array[string]"
}
```

### **Source-Specific Data** (JSON field)
```json
{
  "source_specific": {
    "bali_home_immo": {
      "payment_term": "monthly",
      "monthly_costs": {...},
      "pet_policy": "Pet-friendly"
    },
    "betterplace": {
      "ownership_type": "Leasehold",
      "price_usd": "299,000",
      "lease_duration": "26 Years",
      "roi_available": true
    }
  }
}
```

## 🎯 **IMPLEMENTATION STRATEGY**

### **1. Database Schema**
```sql
CREATE TABLE listings (
  id SERIAL PRIMARY KEY,
  -- Core unified fields
  title VARCHAR(500) NOT NULL,
  location VARCHAR(200) NOT NULL,
  bedrooms INTEGER,
  bathrooms INTEGER,
  property_type VARCHAR(100),
  property_id VARCHAR(100),
  description TEXT,
  images JSON,
  detail_url VARCHAR(500),
  source VARCHAR(100),
  
  -- Extended unified fields  
  land_size_sqm INTEGER,
  building_size_sqm INTEGER,
  year_built INTEGER,
  furnishing VARCHAR(100),
  pool_type VARCHAR(100),
  parking_type VARCHAR(100),
  amenities JSON,
  
  -- Source-specific data
  source_specific JSON,
  
  -- Meta fields
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE
);
```

### **2. API Response Format**
```json
{
  "total": 1427,
  "properties": [
    {
      "id": 1,
      "title": "Modern 3 Bedroom Villa...",
      "location": "Kedungu",
      "bedrooms": 3,
      "bathrooms": 4,
      "price": {
        "display": "IDR 4,866,049,227",
        "usd": "299,000",
        "currency": "IDR"
      },
      "images": ["url1", "url2", "..."],
      "source": "betterplace",
      "source_specific": {
        "ownership_type": "Leasehold",
        "lease_duration": "26 Years",
        "roi_available": true
      }
    }
  ]
}
```

### **3. Search & Filter Capabilities**
```json
{
  "filters": {
    "location": ["Canggu", "Seminyak", "Kedungu"],
    "bedrooms": [1, 2, 3, 4, 5],
    "property_type": ["Villa", "Apartment", "Land"],
    "source": ["bali_home_immo", "betterplace"],
    "price_range": {"min": 0, "max": 5000000000},
    "ownership_type": ["Leasehold", "Freehold", "Rental"],
    "features": ["Private Pool", "Pet Friendly", "Furnished"]
  }
}
```

## 🚀 **NEXT STEPS**

1. **Update Database Models** - Implement unified schema
2. **Update Ingestion Service** - Handle source-specific fields
3. **Build Unified API** - Single endpoint for all sources
4. **Implement Cross-Source Search** - Filter across both sources
5. **Add More Sources** - Villa-Bali.com, etc. using same pattern

## 💡 **KEY INSIGHTS**

- **BetterPlace is much richer** in investment-focused data (31 unique fields)
- **Bali Home Immo specializes** in rental-specific information (12 unique fields)
- **Only 27% of fields are truly unified** - need flexible schema
- **JSON storage for source-specific data** is essential for scalability
- **Price handling needs special attention** - different formats per source
