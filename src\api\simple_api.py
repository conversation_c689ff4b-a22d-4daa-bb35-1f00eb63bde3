#!/usr/bin/env python3
"""
Simple API Server for Testing
Basic property search without complex dependencies
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import json

app = FastAPI(
    title="Bali Property Search API",
    description="Simple API for testing property search",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class PropertySearchRequest(BaseModel):
    """Search request model"""
    location: Optional[str] = None
    bedrooms: Optional[int] = None
    bathrooms: Optional[int] = None
    max_price: Optional[int] = None
    min_price: Optional[int] = None
    property_type: Optional[str] = None
    term: Optional[str] = None
    furnished: Optional[bool] = None
    pool: Optional[bool] = None
    pet_friendly: Optional[bool] = None
    limit: Optional[int] = 10

class PropertyResponse(BaseModel):
    """Property response model"""
    id: str
    title: str
    location: str
    bedrooms: int
    bathrooms: int
    price: Dict[str, Any]
    property_type: str
    source: str
    images: List[str]
    highlights: List[str]
    detail_url: str

class SearchResponse(BaseModel):
    """Search response model"""
    total: int
    properties: List[PropertyResponse]
    filters_applied: Dict[str, Any]
    sources: Dict[str, int]

# Sample data for testing
SAMPLE_PROPERTIES = [
    {
        "id": "1",
        "title": "Cozy 2 Bedroom Villa in Canggu",
        "location": "Canggu",
        "bedrooms": 2,
        "bathrooms": 2,
        "price": {"display": 25000000, "currency": "IDR", "type": "rental"},
        "property_type": "Villa",
        "source": "bali_home_immo",
        "images": ["image1.jpg", "image2.jpg"],
        "highlights": ["Swimming Pool", "Pet Friendly"],
        "detail_url": "https://example.com/property/1"
    },
    {
        "id": "2", 
        "title": "Modern 3 Bedroom Villa with Pool",
        "location": "Seminyak",
        "bedrooms": 3,
        "bathrooms": 3,
        "price": {"display": 3500, "currency": "USD", "type": "rental"},
        "property_type": "Villa",
        "source": "bali_villa_realty",
        "images": ["image3.jpg", "image4.jpg"],
        "highlights": ["Swimming Pool", "Furnished", "Modern"],
        "detail_url": "https://example.com/property/2"
    },
    {
        "id": "3",
        "title": "Investment Property in Kedungu",
        "location": "Kedungu", 
        "bedrooms": 2,
        "bathrooms": 2,
        "price": {"display": 250000, "currency": "USD", "type": "purchase"},
        "property_type": "Villa",
        "source": "betterplace",
        "images": ["image5.jpg", "image6.jpg"],
        "highlights": ["Investment", "Leasehold", "Beach Access"],
        "detail_url": "https://example.com/property/3"
    },
    {
        "id": "4",
        "title": "Pet-Friendly Apartment in Ubud",
        "location": "Ubud",
        "bedrooms": 1,
        "bathrooms": 1,
        "price": {"display": 15000000, "currency": "IDR", "type": "rental"},
        "property_type": "Apartment",
        "source": "bali_home_immo",
        "images": ["image7.jpg"],
        "highlights": ["Pet Friendly", "Garden View"],
        "detail_url": "https://example.com/property/4"
    },
    {
        "id": "5",
        "title": "Luxury 4 Bedroom Villa with Pool",
        "location": "Jimbaran",
        "bedrooms": 4,
        "bathrooms": 4,
        "price": {"display": 8500, "currency": "USD", "type": "rental"},
        "property_type": "Villa",
        "source": "bali_villa_realty",
        "images": ["image8.jpg", "image9.jpg", "image10.jpg"],
        "highlights": ["Swimming Pool", "Luxury", "Ocean View"],
        "detail_url": "https://example.com/property/5"
    }
]

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Bali Property Search API",
        "version": "1.0.0",
        "status": "active",
        "sources": ["bali_home_immo", "betterplace", "bali_villa_realty"],
        "total_properties": len(SAMPLE_PROPERTIES)
    }

@app.get("/api/stats")
async def get_stats():
    """Get overall statistics"""
    return {
        "total_listings": len(SAMPLE_PROPERTIES),
        "available_listings": len(SAMPLE_PROPERTIES),
        "sources": {
            "bali_home_immo": 2,
            "betterplace": 1,
            "bali_villa_realty": 2
        }
    }

@app.post("/api/search", response_model=SearchResponse)
async def search_properties(request: PropertySearchRequest):
    """Search properties based on filters"""
    try:
        # Filter properties based on request
        filtered_properties = []
        
        for prop in SAMPLE_PROPERTIES:
            # Apply location filter
            if request.location and request.location.lower() not in prop["location"].lower():
                continue
            
            # Apply bedroom filter
            if request.bedrooms and prop["bedrooms"] != request.bedrooms:
                continue
            
            # Apply bathroom filter
            if request.bathrooms and prop["bathrooms"] != request.bathrooms:
                continue
            
            # Apply price filter (simplified)
            if request.max_price:
                prop_price = prop["price"]["display"]
                if prop["price"]["currency"] == "USD":
                    prop_price = prop_price * 16000  # Convert to IDR for comparison
                if prop_price > request.max_price:
                    continue
            
            # Apply property type filter
            if request.property_type and prop["property_type"] != request.property_type:
                continue
            
            # Apply term filter
            if request.term:
                if request.term == "purchase" and prop["price"]["type"] != "purchase":
                    continue
                elif request.term in ["monthly", "yearly"] and prop["price"]["type"] != "rental":
                    continue
            
            # Apply pool filter
            if request.pool and "Swimming Pool" not in prop["highlights"]:
                continue
            
            # Apply pet friendly filter
            if request.pet_friendly and "Pet Friendly" not in prop["highlights"]:
                continue
            
            filtered_properties.append(prop)
        
        # Limit results
        limited_properties = filtered_properties[:request.limit]
        
        # Convert to response format
        properties = []
        for prop in limited_properties:
            properties.append(PropertyResponse(**prop))
        
        # Count by source
        sources = {}
        for prop in filtered_properties:
            source = prop["source"]
            sources[source] = sources.get(source, 0) + 1
        
        return SearchResponse(
            total=len(filtered_properties),
            properties=properties,
            filters_applied=request.dict(exclude_none=True),
            sources=sources
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search error: {str(e)}")

@app.get("/api/locations")
async def get_locations():
    """Get available locations"""
    locations = list(set([prop["location"] for prop in SAMPLE_PROPERTIES]))
    locations.sort()
    
    return {
        "locations": locations,
        "total": len(locations)
    }

@app.post("/api/chat")
async def chat_endpoint(request: Dict[str, Any]):
    """Simple chat endpoint (demo mode)"""
    try:
        message = request.get('message', '')
        
        if not message:
            raise HTTPException(status_code=400, detail="Message is required")
        
        # Simple keyword-based response
        message_lower = message.lower()
        
        if any(word in message_lower for word in ['canggu', 'seminyak', 'ubud', 'jimbaran', 'kedungu']):
            response = f"I understand you're looking for properties. Let me search for: {message}"
            
            # Extract simple parameters
            search_params = {}
            if 'canggu' in message_lower:
                search_params['location'] = 'Canggu'
            elif 'seminyak' in message_lower:
                search_params['location'] = 'Seminyak'
            elif 'ubud' in message_lower:
                search_params['location'] = 'Ubud'
            elif 'jimbaran' in message_lower:
                search_params['location'] = 'Jimbaran'
            elif 'kedungu' in message_lower:
                search_params['location'] = 'Kedungu'
            
            if 'bedroom' in message_lower:
                for i in range(1, 6):
                    if f'{i} bedroom' in message_lower:
                        search_params['bedrooms'] = i
                        break
            
            if 'pool' in message_lower:
                search_params['pool'] = True
            
            if 'pet' in message_lower:
                search_params['pet_friendly'] = True
            
            # Perform search
            search_request = PropertySearchRequest(**search_params)
            search_result = await search_properties(search_request)
            
            if search_result.total > 0:
                response = f"I found {search_result.total} properties matching your criteria. Here are the top results:\n\n"
                for prop in search_result.properties[:3]:
                    price_display = f"{prop.price['currency']} {prop.price['display']:,}"
                    if prop.price['type'] == 'rental':
                        price_display += " per month"
                    response += f"• {prop.title}\n  📍 {prop.location} | 🛏️ {prop.bedrooms}BR | 💰 {price_display}\n  ✨ {', '.join(prop.highlights)}\n\n"
            else:
                response = "I couldn't find any properties matching your criteria. Would you like to adjust your search?"
        
        else:
            response = "I'm a property assistant for Bali. I can help you find villas, apartments, and houses. Try asking about properties in Canggu, Seminyak, Ubud, Jimbaran, or Kedungu!"
        
        return {
            "success": True,
            "response": response,
            "session_id": "demo-session",
            "handoff_triggered": False
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chat error: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Bali Property Search API...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔍 Test endpoint: http://localhost:8000/api/stats")
    uvicorn.run(app, host="0.0.0.0", port=8000)
