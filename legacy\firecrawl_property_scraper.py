#!/usr/bin/env python3
"""
Bali Home Immo Property Scraper using Firecrawl MCP Server
Scrapes all rental properties from bali-home-immo.com
"""

import json
import time
from typing import List, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FirecrawlPropertyScraper:
    def __init__(self):
        self.base_url = "https://bali-home-immo.com"
        self.rental_url = "https://bali-home-immo.com/realestate-property/for-rent"
        self.properties = []
        
        # Property schema for extraction
        self.property_schema = {
            "type": "object",
            "properties": {
                "title": {"type": "string"},
                "price": {"type": "string"},
                "payment_term": {"type": "string"},
                "location": {"type": "string"},
                "bedrooms": {"type": "integer"},
                "bathrooms": {"type": "integer"},
                "description": {"type": "string"},
                "amenities": {"type": "array", "items": {"type": "string"}},
                "size": {
                    "type": "object",
                    "properties": {
                        "land_size_sqm": {"type": "integer"},
                        "building_size_sqm": {"type": "integer"}
                    }
                },
                "year_built": {"type": "integer"},
                "property_type": {"type": "string"},
                "property_id": {"type": "string"},
                "status": {"type": "string"},
                "furnishing": {"type": "string"},
                "road_access": {"type": "string"},
                "view": {"type": "string"},
                "imb_status": {"type": "string"},
                "ownership": {"type": "string"},
                "lease_duration": {"type": "string"},
                "zoning": {"type": "string"},
                "floor_level": {"type": "integer"},
                "internet": {"type": "string"},
                "electricity_capacity": {"type": "string"},
                "water_source": {"type": "string"},
                "parking": {"type": "string"},
                "facility_layout": {"type": "string"},
                "brochure_url": {"type": "string"},
                "detail_url": {"type": "string"}
            },
            "required": ["title", "price", "location", "bedrooms", "bathrooms", "property_id", "detail_url"]
        }
    
    def filter_property_urls(self, all_urls: List[str]) -> List[str]:
        """
        Filter URLs to get only actual property detail pages
        """
        property_urls = []
        
        for url in all_urls:
            # Skip category pages and other non-property URLs
            if (url.count('/') >= 7 and  # Property URLs have more path segments
                not url.endswith('/monthly') and
                not url.endswith('/yearly') and
                not url.endswith('/villa') and
                not url.endswith('/other') and
                not url.endswith('/for-rent') and
                'realestate-property/for-rent/' in url):
                
                # Additional filters to exclude category pages
                path_parts = url.split('/')
                if len(path_parts) > 7 and not path_parts[-1] in ['monthly', 'yearly', 'villa', 'other']:
                    property_urls.append(url)
        
        logger.info(f"Filtered {len(property_urls)} property URLs from {len(all_urls)} total URLs")
        return property_urls
    
    def process_properties_batch(self, urls: List[str], batch_size: int = 5) -> List[Dict[str, Any]]:
        """
        Process properties in batches using Firecrawl extract
        Note: This function shows the structure but would need to be called via MCP server
        """
        all_properties = []
        
        for i in range(0, len(urls), batch_size):
            batch_urls = urls[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}: URLs {i+1}-{min(i+batch_size, len(urls))}")
            
            try:
                # This would be called via MCP server in practice:
                # firecrawl_extract_mcp-server-firecrawl with batch_urls
                
                # For demonstration, we'll create sample data
                for url in batch_urls:
                    property_data = self.create_sample_property(url)
                    all_properties.append(property_data)
                
                # Add delay between batches to be respectful
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"Error processing batch: {str(e)}")
                continue
        
        return all_properties
    
    def create_sample_property(self, url: str) -> Dict[str, Any]:
        """
        Create sample property data (for demonstration)
        In practice, this would be replaced by actual Firecrawl extraction
        """
        # Extract property ID from URL
        property_id = url.split('-')[-1] if '-' in url else "UNKNOWN"
        
        return {
            "title": f"Sample Property - {property_id}",
            "price": "50.000.000 IDR / month",
            "payment_term": "Monthly",
            "location": "Sample Location, Bali",
            "bedrooms": 2,
            "bathrooms": 2,
            "description": "Sample property description extracted from the page.",
            "amenities": [
                "Private Pool",
                "Fully Furnished",
                "Air Conditioning",
                "High-Speed Internet",
                "Parking Area"
            ],
            "size": {
                "land_size_sqm": 200,
                "building_size_sqm": 120
            },
            "year_built": 2021,
            "property_type": "Villa",
            "property_id": property_id,
            "status": "Available Now",
            "furnishing": "Fully Furnished",
            "view": "Garden",
            "internet": "Fiber Optic",
            "electricity_capacity": "5.500 Watt",
            "water_source": "Drilled Well",
            "parking": "Carport",
            "facility_layout": "Enclosed Living",
            "detail_url": url
        }
    
    def save_to_json(self, properties: List[Dict[str, Any]], filename: str = "bali_properties_complete.json"):
        """
        Save scraped properties to JSON file
        """
        logger.info(f"Saving {len(properties)} properties to {filename}")
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(properties, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Data saved successfully to {filename}")
    
    def generate_summary_report(self, properties: List[Dict[str, Any]]):
        """
        Generate a summary report of scraped properties
        """
        if not properties:
            logger.warning("No properties to analyze")
            return
        
        # Basic statistics
        total_properties = len(properties)
        locations = {}
        property_types = {}
        bedroom_counts = {}
        
        for prop in properties:
            # Count locations
            location = prop.get('location', 'Unknown')
            locations[location] = locations.get(location, 0) + 1
            
            # Count property types
            prop_type = prop.get('property_type', 'Unknown')
            property_types[prop_type] = property_types.get(prop_type, 0) + 1
            
            # Count bedrooms
            bedrooms = prop.get('bedrooms', 0)
            bedroom_counts[bedrooms] = bedroom_counts.get(bedrooms, 0) + 1
        
        # Print summary
        print(f"\n{'='*50}")
        print(f"BALI PROPERTY SCRAPING SUMMARY")
        print(f"{'='*50}")
        print(f"Total Properties Scraped: {total_properties}")
        print(f"\nTop Locations:")
        for location, count in sorted(locations.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {location}: {count} properties")
        
        print(f"\nProperty Types:")
        for prop_type, count in sorted(property_types.items(), key=lambda x: x[1], reverse=True):
            print(f"  {prop_type}: {count} properties")
        
        print(f"\nBedroom Distribution:")
        for bedrooms, count in sorted(bedroom_counts.items()):
            print(f"  {bedrooms} bedrooms: {count} properties")
        print(f"{'='*50}\n")

def main():
    """
    Main function to demonstrate the scraper structure
    """
    scraper = FirecrawlPropertyScraper()
    
    # Sample URLs (in practice, these would come from firecrawl_map)
    sample_urls = [
        "https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/pandawa/cozy-2-bedrooms-villa-for-yearly-and-monthly-rental-in-bali-pandawa-kutuh-ad019",
        "https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/2-bedroom-townhouse-for-rent-in-berawa-rf1508",
        "https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/seseh/2-bedroom-villa-with-rooftop-for-monthly-rental-near-cemagi-beach-rf7743"
    ]
    
    # Filter property URLs
    property_urls = scraper.filter_property_urls(sample_urls)
    
    # Process properties
    properties = scraper.process_properties_batch(property_urls)
    
    # Save results
    scraper.save_to_json(properties)
    
    # Generate summary
    scraper.generate_summary_report(properties)
    
    print(f"Scraping completed! Found {len(properties)} properties.")
    print("Data saved to 'bali_properties_complete.json'")

if __name__ == "__main__":
    main()
