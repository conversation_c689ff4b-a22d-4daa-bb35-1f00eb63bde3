#!/usr/bin/env python3
"""
Simple CLI Chat Interface
Works with the simple API server
"""

import requests
import json
import sys
import time

API_BASE_URL = "http://localhost:8000"

class SimpleChatInterface:
    """Simple chat interface for property search"""
    
    def __init__(self):
        self.session_active = True
        self.conversation_history = []
    
    def display_welcome(self):
        """Display welcome message"""
        welcome_msg = """
🏡 Welcome to Bali Property Assistant! 🏡

I'm here to help you find the perfect property in Bali. I can search through:
• Budget rentals (IDR 15M-150M/month) 
• Premium furnished rentals (USD 1.5K-10K/month)
• Investment properties (USD 120K-2M+)

Available locations: Canggu, Seminyak, Ubud, Jimbaran, Kedungu

What kind of property are you looking for? You can tell me about:
- Location preferences (e.g., "Canggu", "Seminyak")
- Number of bedrooms (e.g., "2 bedroom")
- Special requirements (e.g., "pool", "pet-friendly")

Let's find your perfect place! 🌴
"""
        print(welcome_msg)
    
    def get_user_input(self):
        """Get user input"""
        try:
            user_input = input("\n💬 You: ").strip()
            if user_input.lower() in ['quit', 'exit', 'bye', 'goodbye']:
                self.session_active = False
                return ""
            return user_input
        except KeyboardInterrupt:
            self.session_active = False
            return ""
    
    def send_message(self, message):
        """Send message to API and get response"""
        try:
            print("\n🤖 Assistant: Thinking...")
            
            # Send to chat endpoint
            response = requests.post(
                f"{API_BASE_URL}/api/chat",
                json={"message": message},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    return data["response"]
                else:
                    return "Sorry, I encountered an error processing your request."
            else:
                return f"Sorry, I'm having trouble connecting to the server (status: {response.status_code})"
                
        except requests.exceptions.ConnectionError:
            return "❌ Cannot connect to the API server. Make sure it's running on http://localhost:8000"
        except requests.exceptions.Timeout:
            return "⏰ Request timed out. Please try again."
        except Exception as e:
            return f"❌ Error: {str(e)}"
    
    def check_api_connection(self):
        """Check if API server is running"""
        try:
            response = requests.get(f"{API_BASE_URL}/", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Connected to API server")
                print(f"   Status: {data['status']}")
                print(f"   Available properties: {data['total_properties']}")
                return True
            else:
                print(f"⚠️ API server responded with status: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Cannot connect to API server: {str(e)}")
            print(f"   Make sure the server is running: python src/api/simple_api.py")
            return False
    
    def display_help(self):
        """Display help information"""
        help_msg = """
🔧 Available Commands:
• Type your property search naturally
• Examples:
  - "Show me 2 bedroom villas in Canggu"
  - "Pet-friendly properties in Seminyak"
  - "Investment properties in Kedungu"
  - "Properties with swimming pool"
• Type 'quit' or 'exit' to end the conversation
• Press Ctrl+C to exit anytime

📍 Available Locations:
• Canggu - Great for surfers and digital nomads
• Seminyak - Beach clubs and nightlife
• Ubud - Rice fields and wellness retreats
• Jimbaran - Family-friendly with seafood
• Kedungu - Emerging area with great views

🏠 Property Types:
• Villa - Private houses with gardens
• Apartment - Modern units in complexes
• Investment - Properties for purchase
"""
        print(help_msg)
    
    def run_chat(self):
        """Main chat loop"""
        print("🚀 Starting Bali Property Assistant CLI...")
        
        # Check API connection
        if not self.check_api_connection():
            print("\n❌ Cannot start chat without API connection.")
            print("   Please start the API server first:")
            print("   python src/api/simple_api.py")
            return
        
        self.display_welcome()
        
        while self.session_active:
            user_input = self.get_user_input()
            
            if not user_input:
                break
            
            if user_input.lower() == "help":
                self.display_help()
                continue
            
            # Send message and get response
            response = self.send_message(user_input)
            print(f"\n🤖 Assistant: {response}")
            
            # Add to history
            self.conversation_history.append({
                "user": user_input,
                "assistant": response,
                "timestamp": time.time()
            })
        
        print("\n👋 Thank you for using Bali Property Assistant! Have a great day!")
    
    def save_conversation(self, filename="conversation_history.json"):
        """Save conversation history"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.conversation_history, f, indent=2, ensure_ascii=False)
            print(f"💾 Conversation saved to {filename}")
        except Exception as e:
            print(f"❌ Error saving conversation: {str(e)}")

def main():
    """Run the chat interface"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Bali Property Assistant CLI Chat')
    parser.add_argument('--save-history', action='store_true', help='Save conversation history')
    parser.add_argument('--api-url', default='http://localhost:8000', help='API base URL')
    
    args = parser.parse_args()
    
    # Update API URL if provided
    global API_BASE_URL
    API_BASE_URL = args.api_url
    
    chat = SimpleChatInterface()
    
    try:
        chat.run_chat()
    finally:
        if args.save_history and chat.conversation_history:
            chat.save_conversation()

if __name__ == "__main__":
    main()
