#!/usr/bin/env python3
"""
OpenAI API Integration for Property Assistant
Implements real AI responses with function calling
"""

import json
import os
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import openai
from openai import OpenAI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ChatMessage:
    """Chat message structure"""
    role: str  # 'system', 'user', 'assistant', 'function'
    content: str
    function_call: Optional[Dict] = None
    name: Optional[str] = None  # For function responses

class OpenAIPropertyAssistant:
    """
    OpenAI-powered property assistant with function calling
    """
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-4-turbo-preview"):
        """
        Initialize OpenAI assistant
        
        Args:
            api_key: OpenAI API key (or set OPENAI_API_KEY env var)
            model: OpenAI model to use
        """
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key required. Set OPENAI_API_KEY environment variable or pass api_key parameter.")
        
        self.client = OpenAI(api_key=self.api_key)
        self.model = model
        self.conversation_history: List[ChatMessage] = []
        
        # Initialize with system prompt
        self.system_prompt = self._get_system_prompt()
        self.conversation_history.append(ChatMessage(
            role="system",
            content=self.system_prompt
        ))
        
        # Function definitions
        self.functions = [self._get_search_function_definition()]
        
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the assistant"""
        return """You are a helpful, friendly property assistant specialized in long-term rentals and real estate in Bali. Your task is to help users find a home by asking conversational, human questions about what they're looking for, such as location, price range, property type, number of bedrooms, bathrooms, desired start date and lease duration.

Always use the `search_properties` function to perform a search once you have enough information. Never invent results. Show a maximum of 3 listings at a time, and offer users the option to refine or broaden their filters. If you're unsure about their request, ask clarifying questions. Keep your tone supportive and to the point.

Available locations in Bali include: Canggu, Seminyak, Ubud, Kedungu, Jimbaran, Pandawa, Padonan, Babakan, Uluwatu, and more.

Property types available: Villa, Apartment, House, Land.

Price ranges vary from IDR 15M-150M per month for rentals, and USD 120K-2M+ for purchases.

You have access to three property sources:
1. Bali Home Immo - Budget rentals with pet policies and cost breakdowns (IDR 15M-150M/month)
2. BetterPlace - Investment properties with ROI data and leasehold information (USD 120K-2M+)
3. Bali Villa Realty - Premium furnished rentals with multi-currency pricing (USD 1.5K-10K/month)

Always ask clarifying questions if the user's request is unclear, and summarize their preferences before searching. When you find suitable properties, offer to help them contact the property owners or agents.

If a user seems ready to proceed with a property, offer to send their details to our sales team via Slack for personalized assistance."""

    def _get_search_function_definition(self) -> Dict[str, Any]:
        """Get the search function definition for OpenAI function calling"""
        return {
            "name": "search_properties",
            "description": "Search for properties in Bali based on user preferences. Use this function when you have enough information to perform a meaningful search.",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "Location/area in Bali (e.g., Canggu, Seminyak, Ubud, Kedungu, Jimbaran)"
                    },
                    "bedrooms": {
                        "type": "integer",
                        "description": "Number of bedrooms",
                        "minimum": 1,
                        "maximum": 10
                    },
                    "bathrooms": {
                        "type": "integer",
                        "description": "Number of bathrooms", 
                        "minimum": 1,
                        "maximum": 10
                    },
                    "max_price": {
                        "type": "integer",
                        "description": "Maximum price in IDR for rentals or USD for purchases"
                    },
                    "min_price": {
                        "type": "integer",
                        "description": "Minimum price in IDR for rentals or USD for purchases"
                    },
                    "property_type": {
                        "type": "string",
                        "description": "Type of property",
                        "enum": ["Villa", "Apartment", "House", "Land"]
                    },
                    "term": {
                        "type": "string",
                        "description": "Rental term or purchase",
                        "enum": ["monthly", "yearly", "purchase"]
                    },
                    "furnished": {
                        "type": "boolean",
                        "description": "Whether property should be furnished"
                    },
                    "pool": {
                        "type": "boolean",
                        "description": "Whether property should have a swimming pool"
                    },
                    "pet_friendly": {
                        "type": "boolean",
                        "description": "Whether property should be pet-friendly"
                    }
                },
                "required": []
            }
        }
    
    def add_message(self, role: str, content: str, function_call: Optional[Dict] = None, name: Optional[str] = None):
        """Add a message to conversation history"""
        message = ChatMessage(
            role=role,
            content=content,
            function_call=function_call,
            name=name
        )
        self.conversation_history.append(message)
    
    def _messages_to_openai_format(self) -> List[Dict[str, Any]]:
        """Convert conversation history to OpenAI API format"""
        messages = []
        for msg in self.conversation_history:
            openai_msg = {
                "role": msg.role,
                "content": msg.content
            }
            if msg.function_call:
                openai_msg["function_call"] = msg.function_call
            if msg.name:
                openai_msg["name"] = msg.name
            messages.append(openai_msg)
        return messages
    
    async def get_response(self, user_message: str, search_function_callback) -> str:
        """
        Get AI response with function calling support
        
        Args:
            user_message: User's input message
            search_function_callback: Function to call for property search
            
        Returns:
            Assistant's response
        """
        try:
            # Add user message to history
            self.add_message("user", user_message)
            
            # Prepare messages for OpenAI
            messages = self._messages_to_openai_format()
            
            # Make API call with function calling
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                functions=self.functions,
                function_call="auto",
                temperature=0.7,
                max_tokens=1000
            )
            
            message = response.choices[0].message
            
            # Check if function was called
            if message.function_call:
                function_name = message.function_call.name
                function_args = json.loads(message.function_call.arguments)
                
                logger.info(f"Function called: {function_name} with args: {function_args}")
                
                # Add function call to history
                self.add_message(
                    "assistant", 
                    message.content or "",
                    function_call={
                        "name": function_name,
                        "arguments": message.function_call.arguments
                    }
                )
                
                # Execute function
                if function_name == "search_properties":
                    search_results = await search_function_callback(**function_args)
                    
                    # Add function result to history
                    function_result = json.dumps(search_results)
                    self.add_message("function", function_result, name=function_name)
                    
                    # Get final response with search results
                    messages = self._messages_to_openai_format()
                    final_response = self.client.chat.completions.create(
                        model=self.model,
                        messages=messages,
                        temperature=0.7,
                        max_tokens=1500
                    )
                    
                    final_content = final_response.choices[0].message.content
                    self.add_message("assistant", final_content)
                    return final_content
            
            else:
                # No function call, regular response
                content = message.content
                self.add_message("assistant", content)
                return content
                
        except Exception as e:
            logger.error(f"OpenAI API error: {str(e)}")
            error_response = "I'm sorry, I'm having trouble processing your request right now. Could you please try again?"
            self.add_message("assistant", error_response)
            return error_response
    
    def get_conversation_summary(self) -> str:
        """Get a summary of the current conversation for Slack handoff"""
        try:
            # Get last few messages for context
            recent_messages = self.conversation_history[-10:]  # Last 10 messages
            
            conversation_text = ""
            for msg in recent_messages:
                if msg.role in ["user", "assistant"]:
                    role_label = "User" if msg.role == "user" else "Assistant"
                    conversation_text += f"{role_label}: {msg.content}\n"
            
            # Create summary prompt
            summary_prompt = f"""Please create a brief summary of this property search conversation for a sales agent. Include:
1. What the user is looking for (location, bedrooms, budget, etc.)
2. Any specific requirements mentioned
3. Current status of the search
4. Next steps needed

Conversation:
{conversation_text}

Provide a concise summary in 2-3 sentences."""

            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": summary_prompt}],
                temperature=0.3,
                max_tokens=200
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error creating summary: {str(e)}")
            return "Property search conversation - summary generation failed"
    
    def reset_conversation(self):
        """Reset conversation history"""
        self.conversation_history = [ChatMessage(
            role="system",
            content=self.system_prompt
        )]
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get conversation history in JSON format"""
        return [
            {
                "role": msg.role,
                "content": msg.content,
                "function_call": msg.function_call,
                "name": msg.name
            }
            for msg in self.conversation_history
        ]
