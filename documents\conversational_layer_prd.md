# 🗣️ Conversational Layer & UX PRD (AI Assistant)

## 🎯 <PERSON><PERSON> spraak- en chatgebaseerde AI Assistant bouwen die woningzoekers op Bali helpt bij het vinden van passende huur- of koopwoningen, op basis van natuurlijke conversaties. De Assistant interpreteert gebruikersbehoeften, zet deze om in gestructureerde zoekfilters (function calling), en haalt resultaten op uit de interne API die gevoed wordt door Firecrawl.

---

## 🧩 Componenten

1. **System Prompt & Assistant Personality**
2. **Function Calling Schema (search\_properties)**
3. **Gespreksflow & Conversatiebeheer**
4. **Spraak & Chat UI**
5. **<PERSON><PERSON><PERSON><PERSON> met Backend Search API**
6. **Whisper Integratie (spraak naar tekst)**

---

## 1. System Prompt & Assistant Personality

De Assistant:

- Pra<PERSON> vriendelijk, duidelijk en menselijk
- Vraagt op natuurlijke manier naar voorkeuren
- Doet geen aannames of beloftes buiten data om
- Vat voorkeuren samen voordat hij suggesties toont

**Prompt (ENGLISH VERSION - MVP)**:

> You are a helpful, friendly property assistant specialized in long-term rentals and real estate in Bali. Your task is to help users find a home by asking conversational, human questions about what they’re looking for, such as location, price range, property type, number of bedrooms, bathrooms, desired start date and lease duration.
>
> Always use the `search_properties` function to perform a search once you have enough information. Never invent results. Show a maximum of 3 listings at a time, and offer users the option to refine or broaden their filters. If you're unsure about their request, ask clarifying questions. Keep your tone supportive and to the point.

---

## 2. Function Calling: `search_properties`

Definieer filters voor het zoeken in de listings database. Schema staat in het aparte JSON document `Ai-assistant-function-calling`.

**Belangrijk:**

- Assistant mag alleen deze functie gebruiken om listings op te halen
- Validatie op locatie, property\_type, prijs etc. gebeurt op backend

---

## 3. Gespreksflow & Conversatiestates

| Fase              | Doel                          | Acties                                             |
| ----------------- | ----------------------------- | -------------------------------------------------- |
| 1. Verkenning     | Begrijpen wat gebruiker zoekt | Vragen naar locatie, termijn, type, budget, kamers |
| 2. Samenvatting   | Herhalen van input            | Vat filters samen en vraag bevestiging             |
| 3. Zoekresultaten | Presenteren van matches       | Toon max. 3 resultaten, vraag of ze meer willen    |
| 4. Hulp aanbieden | Optioneel vervolg             | Aanbieden om contact te regelen of meer te zoeken  |

Fallbacks:

- Geen matches? Stel voor om filters te verruimen
- Onduidelijk antwoord? Vraag op andere manier door

---

## 4. Spraak & Chat UI

Voor MVP starten met chat-UI (eventueel later uitbreiden met voice):

**Chatinterface moet kunnen:**

- Gespreksgeschiedenis tonen
- Voorkeuren bewaren in session
- Antwoorden formatten in cards (listing cards)
- Terugvraagknoppen (quick replies: "Toon meer", "Andere locatie")

**Spraak (optioneel later)**

- Whisper API (spraak → tekst)
- ElevenLabs of Web Speech API (tekst → spraak)

---

## 5. Integratie Backend API

Gebruik de API uit het technische PRD (o.a. `POST /api/search`) om data op te halen:

- Input = JSON gegenereerd via `search_properties`
- Output = lijst met listings in uniform formaat
- Assistant mag alleen antwoorden op basis van deze resultaten

Voorbeeld:

```json
{
  "location": "Canggu",
  "bedrooms": 2,
  "max_price": 25000000,
  "term": "yearly"
}
```

---

## 6. Whisper Integratie (spraak naar tekst)

**Gebruik:** Whisper wordt ingezet om gesproken input van gebruikers te transcriberen naar tekst die door de Assistant verwerkt kan worden.

**Flow:**

1. Gebruiker spreekt in via microfoon (browser of app)
2. Audio wordt als bestand verstuurd naar Whisper API
3. Whisper retourneert de getranscribeerde tekst
4. Deze tekst gaat als gewone input het GPT-assistantproces in

**Voordelen:**

- Accurate transcriptie van spraak
- Meertalig met accentondersteuning

**Beperkingen:**

- Niet real-time (verwerking van 1-3 seconden per opname)
- Kwaliteit afhankelijk van ruis en microfoon

**Fallback:**

- Als Whisper geen bruikbaar resultaat geeft → vraag gebruiker om te herhalen of te typen

**Later uitbreidbaar met:**

- Realtime voice UI via WebRTC + Whisper streaming
- Offline transcription (met open Whisper modellen)

---

## MVP Scope (Binnen 1 week mogelijk):

- ✅ Function calling op basis van gebruikersinput
- ✅ Prompt + conversatiesturing via GPT-4 turbo
- ✅ Chat UI (Next.js of vergelijkbaar)
- ✅ Integratie met eigen search API
- ✅ Whisper transcriptie als optionele invoerlaag

Voice responses, memory en multi-turn refinement zijn optioneel voor v2.

---

Laatste stap: testen met realistische gebruikersvragen zoals:

- "We zoeken iets met 3 slaapkamers in Berawa voor max 20 miljoen per maand"
- "Is er iets beschikbaar in Ubud voor een gezin?"
- "Mag ook semi-furnished zijn, als er een zwembad is"

Resultaten moeten correct, beknopt en relevant zijn. Assistant moet weten wat hij *niet* weet en nooit informatie verzinnen.

