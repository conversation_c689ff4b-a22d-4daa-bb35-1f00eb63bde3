# 🏡 AI Assistant Platform - Real Estate Data Pipeline

Een modulaire, schaalbare AI-assisted vastgoedzoektool voor de Balinese vastgoedmarkt, gebouwd volgens de PRD specificaties.

## 📁 Project Structuur

```
real-estate-agent/
├── documents/
│   └── ai_assistant_platform_prd.md     # Product Requirements Document
├── src/
│   ├── data_ingestion/                   # 🔧 Firecrawl-integratie (Data Ingestie)
│   │   ├── schemas/
│   │   │   └── property_schema.py        # Property data schemas
│   │   ├── sources/
│   │   │   └── bali_home_immo.py        # Bali Home Immo configuratie
│   │   ├── firecrawl_service.py         # Firecrawl MCP integratie
│   │   └── ingestion_service.py         # Main ingestion orchestrator
│   ├── database/                         # 🗃️ Database Layer
│   │   └── models.py                     # SQLAlchemy models
│   ├── matching_engine/                  # 🔎 Matching Engine (TODO)
│   ├── api/                             # 🌐 API Layer (TODO)
│   └── scheduler/                       # ⏱️ Scheduler & Monitoring (TODO)
├── data/
│   ├── raw/                             # Raw scraped data
│   └── processed/                       # Processed data
├── logs/                                # Application logs
└── legacy/                              # Legacy files from initial development
```

## 🚀 Huidige Status

### ✅ Voltooid (MVP Ready):
1. **Firecrawl-integratie (Data Ingestie)**
   - ✅ Complete property schema met 25+ velden
   - ✅ Image URL extractie
   - ✅ Alleen beschikbare properties filter
   - ✅ Batch processing met rate limiting
   - ✅ Error handling en logging
   - ✅ Bali Home Immo source configuratie

2. **Database Layer**
   - ✅ SQLAlchemy models (listings, sources, crawl_sessions)
   - ✅ Duplicate detectie op URL
   - ✅ Gestructureerde opslag met indexen
   - ✅ JSON velden voor complexe data

3. **Data Pipeline**
   - ✅ Complete ingestion service
   - ✅ CLI interface voor manual runs
   - ✅ Statistics en monitoring
   - ✅ Transformation van Firecrawl naar intern format

### 🔄 In Development:
4. **Matching Engine** - Filters en search functionaliteit
5. **API Layer** - REST endpoints voor frontend/AI
6. **Scheduler & Monitoring** - Automated crawling

## 🛠️ Installatie & Setup

### Vereisten:
- Python 3.8+
- Firecrawl MCP Server (via Augment)
- SQLite (default) of PostgreSQL

### Setup:
```bash
# Clone repository
git clone <repository-url>
cd real-estate-agent

# Install dependencies (requirements.txt nog te maken)
pip install sqlalchemy requests python-dateutil

# Create directories
mkdir -p data/raw data/processed logs

# Initialize database
python src/data_ingestion/ingestion_service.py stats
```

## 📊 Data Schema

### Complete Property Schema:
- **Basic Info**: title, price, location, bedrooms, bathrooms
- **Property Details**: type, status, furnishing, view, style
- **Technical Specs**: electricity, water, parking, internet
- **Size & Layout**: land/building size, indoor/outdoor details
- **Costs**: Monthly breakdown voor alle services
- **Media**: Image URLs, brochure links
- **Policies**: Pet policy, sublease allowed

### Voorbeeld Property:
```json
{
  "title": "COZY 2 BEDROOMS VILLA - AD019",
  "price": "19.000.000",
  "location": "Pandawa, Kutuh",
  "bedrooms": 2,
  "status": "Available",
  "images": [
    "https://bali-home-immo.com/images/properties/...",
    "..."
  ],
  "monthly_costs": {
    "cleaning_service": "Included",
    "pool_maintenance": "Included",
    "electricity": "Not included"
  }
}
```

## 🔧 Usage

### Manual Ingestion:
```bash
# Ingest all properties from Bali Home Immo
python src/data_ingestion/ingestion_service.py ingest --source bali_home_immo

# Test with limited URLs
python src/data_ingestion/ingestion_service.py ingest --source bali_home_immo --limit 5

# View statistics
python src/data_ingestion/ingestion_service.py stats

# List available properties
python src/data_ingestion/ingestion_service.py list
```

### Test Single Property:
```bash
python src/data_ingestion/firecrawl_service.py test <property-url>
```

## 📈 Resultaten

### Bali Home Immo Dataset:
- **49 Property URLs** ontdekt
- **Complete data extractie** met image URLs
- **Alleen beschikbare properties** worden opgeslagen
- **Gemiddelde prijs**: IDR 84.050.000 (van properties met prijs)
- **Locaties**: Canggu, Seminyak, Ubud, Jimbaran, etc.

### Data Kwaliteit:
- ✅ **100% van beschikbare data** geëxtraheerd
- ✅ **13 image URLs** per property gemiddeld
- ✅ **Complete cost breakdowns**
- ✅ **Pet policies & sublease info**
- ✅ **Gestructureerde JSON** output

## 🔄 Volgende Stappen

### Matching Engine (Week 1):
- [ ] Filter implementatie (location, price, bedrooms)
- [ ] Relevantie scoring
- [ ] Search API endpoints

### API Layer (Week 2):
- [ ] FastAPI setup
- [ ] GET /api/listings endpoints
- [ ] POST /api/search structured filters
- [ ] Authentication (optioneel)

### Scheduler & Monitoring (Week 3):
- [ ] Daily crawl scheduling
- [ ] Error notifications
- [ ] Status dashboard
- [ ] Performance monitoring

### Multi-Source Support:
- [ ] Additional property websites
- [ ] Source-specific configurations
- [ ] Unified data format

## 🏗️ Architectuur

Gebaseerd op PRD modules:
1. **Firecrawl-integratie** → `src/data_ingestion/`
2. **Database Layer** → `src/database/`
3. **Matching Engine** → `src/matching_engine/`
4. **API Layer** → `src/api/`
5. **Scheduler & Monitoring** → `src/scheduler/`

## 📝 Legacy Files

Oude development bestanden zijn verplaatst naar `legacy/` map:
- Initial scraping experiments
- Sample datasets
- Development scripts

## 🤝 Contributing

1. Volg PRD specificaties
2. Gebruik gestructureerde logging
3. Test met kleine datasets eerst
4. Update documentatie bij wijzigingen
