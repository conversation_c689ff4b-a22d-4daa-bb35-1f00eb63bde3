# 🧠 AI Assistant Platform PRD (Technische Modules voor Development in VS Code)

## 🔍 Doel

Een modulaire, schaalbare AI-assisted vastgoedzoektool ontwikkelen die vraag en aanbod bij elkaar brengt in de gefragmenteerde Balinese vastgoedmarkt. Dit PRD beschrijft uitsluitend de onderdelen die via code gebouwd moeten worden in een lokale ontwikkelomgeving (VS Code), zoals de data pipeline (via Firecrawl), database, API's en backend-logica.

> Conversational AI (LLM prompts, voice/chat frontend, user experience) wordt beschreven in een afzonderlijk document.

---

## 📦 Hoofcomponenten (Dev Modules)

1. **Firecrawl-integratie (Data Ingestie)**
2. **Database Layer (Listings DB)**
3. **Matching Engine (Filters & Search)**
4. **API Layer (REST for frontend + AI)**
5. **Scheduler & Monitoring**

---

## 1. 🔧 Firecrawl-integratie (Data Ingestie)

### Doel

Listings ophalen van externe websites via de [Firecrawl API](https://docs.firecrawl.dev), inclusief volledige details van individuele propertypagina’s.

### Functionaliteiten

- Aanroepen van Firecrawl Extract API met aangepaste prompts
- Instructie voor crawldiepte (`maxDepth: 1`, `followLinks: true`)
- Ontvangen en opslaan van JSON-resultaat
- Mapping naar interne datastructuur
- Transformeren naar uniform formaat voor opslag

### Outputformaat (vereenvoudigd)

```json
{
  "title": "...",
  "price": "...",
  "location": "...",
  "bedrooms": 2,
  "bathrooms": 2,
  "description": "...",
  "amenities": [...],
  "additional_details": {
    "size": "200 m²",
    "year_built": 2021,
    "property_type": "Villa"
  },
  "source_url": "..."
}
```

### Eisen

- Aanpasbare prompts per aanbieder (koop, huur, locatie)
- Transformatiestap om Firecrawl output te converteren naar ons gewenste datamodel
- Error logging bij mislukte requests
- Mogelijkheid tot manuele testruns (dev CLI)

### Tools

- Node.js of Python script om Firecrawl API aan te roepen
- Mapping helper (JS of Python) voor transformeren JSON

---

## 2. 🗃️ Database Layer

### Doel

Gestructureerde opslag van listings, metadata en crawlhistoriek.

### Functionaliteiten

- Opslaan van gestandaardiseerde listing records
- Duplicate detectie (op URL of external ID)
- Markeren van "verwijderd" bij inactiviteit
- Meertalige velden ondersteunen (originele en vertaalde beschrijvingen)

### Tabellen (MongoDB of PostgreSQL voorbeeld)

- `listings`
- `crawl_sessions`
- `sources`

### Vereisten

- Snel querybaar (filters op locatie, type, prijs, etc.)
- Index op locatie, type, prijs

---

## 3. 🔎 Matching Engine

### Doel

Zoekopdrachten van gebruikers (via AI of frontend) vertalen naar filters die op de listings worden toegepast.

### Functionaliteiten

- Ontvangen van zoekcriteria (via API)
- Filteren van listings in DB
- Sorteren op relevantie (bv. dichtst bij budget + locatie)
- Limiteren van resultaten (bv. top 5 matches)

### Voorbeeld filters

- `location in ["Canggu", "Ubud"]`
- `price <= 2000000`
- `bedrooms >= 2`
- `available == True`

---

## 4. 🌐 API Layer

### Doel

Frontend (chat + UI) en AI toegang geven tot listings en zoekfunctionaliteit.

### Endpoints

- `GET /api/listings` (filters: location, price, type, etc.)
- `GET /api/listings/{id}` (detailinfo)
- `POST /api/search` (structured filters vanuit AI)
- `GET /api/stats` (optioneel)

### Stack

- Node.js (Express) of FastAPI (Python)
- Auth optioneel (voor eerste MVP niet nodig)

---

## 5. ⏱️ Scheduler & Monitoring

### Doel

Periodiek aanroepen van Firecrawl + foutdetectie inbouwen.

### Functionaliteiten

- Dagelijkse run per URL (koop/huur)
- Log success/fail per run
- Foutmeldingen naar Slack/email webhook
- Status bijhouden per source (last success, last fail)

### Tools

- Node cron / Python `schedule`
- Dagster (optioneel)

---

## 📌 MVP Scope Checklist

-

---

## 🚫 Wat zit **niet** in dit PRD

- LLM prompts, instructie logica, session memory
- Voice UI (microfoon, TTS/STT integraties)
- Frontend interface (chatvenster, property viewer)

➡️ Deze onderdelen staan in het aparte PRD: **Conversational Layer & UX**

