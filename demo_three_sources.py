#!/usr/bin/env python3
"""
Three-Source Demo
Shows the complete system with Bali Home Immo, BetterPlace, and BaliVillaRealty
"""

import json
import sys
import os
from pathlib import Path

# Add src to path
sys.path.append('src')

def demo_bali_home_immo():
    """Demo Bali Home Immo property"""
    print("🏠 BALI HOME IMMO - RENTAL PROPERTIES")
    print("=" * 50)
    
    sample_property = {
        "title": "COZY 2 BEDROOMS VILLA - AD019",
        "price": "19.000.000",
        "payment_term": "monthly",
        "location": "Pandawa, Kutuh",
        "bedrooms": 2,
        "bathrooms": 2,
        "status": "Available",
        "property_type": "Villa",
        "property_id": "AD019",
        "pet_policy": "Pet-friendly",
        "sublease_allowed": True,
        "monthly_costs": {
            "cleaning_service": "Included",
            "pool_maintenance": "Included",
            "electricity": "Not included"
        },
        "images": 5,
        "source": "bali_home_immo",
        "focus": "Monthly rentals with detailed cost breakdowns"
    }
    
    print(f"✅ Property: {sample_property['title']}")
    print(f"💰 Price: IDR {sample_property['price']} ({sample_property['payment_term']})")
    print(f"📍 Location: {sample_property['location']}")
    print(f"🏠 Type: {sample_property['property_type']} | ID: {sample_property['property_id']}")
    print(f"🛏️ Bedrooms: {sample_property['bedrooms']} | Bathrooms: {sample_property['bathrooms']}")
    print(f"🐕 Pet Policy: {sample_property['pet_policy']}")
    print(f"📸 Images: {sample_property['images']} URLs")
    print(f"💡 Focus: {sample_property['focus']}")
    
    return sample_property

def demo_betterplace():
    """Demo BetterPlace property"""
    print("\n🏢 BETTERPLACE - INVESTMENT PROPERTIES")
    print("=" * 50)
    
    sample_property = {
        "title": "Modern 3 Bedroom Villa with Rooftop in Pangkung Tibah",
        "price_idr": "IDR 4,866,049,227",
        "price_usd": "USD 299,000",
        "ownership_type": "Leasehold",
        "location": "Kedungu",
        "bedrooms": 3,
        "bathrooms": 4,
        "property_type": "Villa",
        "property_id": "BPVL02232",
        "lease_duration": "26 Years",
        "lease_expiry": "Q3 2051",
        "land_size_sqm": 260,
        "building_size_sqm": 166,
        "price_per_sqm": "IDR 29,313,550",
        "distance_to_beach": "3 Minutes",
        "listing_agent": "Lia",
        "agent_whatsapp": "+62 811-3900-2007",
        "images": 31,
        "source": "betterplace",
        "focus": "Investment properties with ROI data and leasehold terms"
    }
    
    print(f"✅ Property: {sample_property['title'][:50]}...")
    print(f"💰 Price: {sample_property['price_idr']} ({sample_property['price_usd']})")
    print(f"🏛️ Ownership: {sample_property['ownership_type']} ({sample_property['lease_duration']})")
    print(f"📍 Location: {sample_property['location']} ({sample_property['distance_to_beach']} to beach)")
    print(f"🏠 Type: {sample_property['property_type']} | ID: {sample_property['property_id']}")
    print(f"🛏️ Bedrooms: {sample_property['bedrooms']} | Bathrooms: {sample_property['bathrooms']}")
    print(f"📐 Size: {sample_property['land_size_sqm']}sqm land, {sample_property['building_size_sqm']}sqm building")
    print(f"👤 Agent: {sample_property['listing_agent']} ({sample_property['agent_whatsapp']})")
    print(f"📸 Images: {sample_property['images']} URLs")
    print(f"💡 Focus: {sample_property['focus']}")
    
    return sample_property

def demo_bali_villa_realty():
    """Demo BaliVillaRealty property"""
    print("\n🏖️ BALI VILLA REALTY - PREMIUM RENTALS")
    print("=" * 50)
    
    sample_property = {
        "title": "Cozy Villa 2 Bedrooms For Rental in Canggu, Bali",
        "price_monthly_usd": "USD 2,885",
        "price_monthly_idr": "IDR 47,300,000",
        "price_yearly_usd": "USD 34,208",
        "price_yearly_idr": "IDR 561,000,000",
        "location": "Canggu",
        "bedrooms": 2,
        "bathrooms": 3,
        "property_type": "Villa",
        "property_id": "IPB00914",
        "rent_status": ["Monthly Rental", "Yearly Rental"],
        "rent_availability": "Available now!",
        "furniture_status": "Fully Furnished",
        "property_size_sqm": 80,
        "land_area_sqm": 80,
        "pool_size": "11 sqm",
        "company_name": "Bali Villa Realty",
        "contact_phone": "+628113899984",
        "contact_email": "<EMAIL>",
        "features": ["Air Conditioning", "Swimming Pool", "Kitchen set", "TV", "Water Dispenser"],
        "currency_options": ["USD", "IDR"],
        "images": 15,
        "source": "bali_villa_realty",
        "focus": "Premium furnished rentals with multi-currency pricing"
    }
    
    print(f"✅ Property: {sample_property['title']}")
    print(f"💰 Monthly: {sample_property['price_monthly_usd']} ({sample_property['price_monthly_idr']})")
    print(f"💰 Yearly: {sample_property['price_yearly_usd']} ({sample_property['price_yearly_idr']})")
    print(f"📍 Location: {sample_property['location']}")
    print(f"🏠 Type: {sample_property['property_type']} | ID: {sample_property['property_id']}")
    print(f"🛏️ Bedrooms: {sample_property['bedrooms']} | Bathrooms: {sample_property['bathrooms']}")
    print(f"📐 Size: {sample_property['property_size_sqm']}sqm property, {sample_property['land_area_sqm']}sqm land")
    print(f"🏊 Pool: {sample_property['pool_size']} private pool")
    print(f"🏢 Company: {sample_property['company_name']} ({sample_property['contact_phone']})")
    print(f"💱 Currencies: {', '.join(sample_property['currency_options'])}")
    print(f"📸 Images: {sample_property['images']} URLs")
    print(f"💡 Focus: {sample_property['focus']}")
    
    return sample_property

def demo_three_source_comparison():
    """Compare all three sources"""
    print("\n🔄 THREE-SOURCE COMPARISON")
    print("=" * 50)
    
    comparison = {
        "bali_home_immo": {
            "focus": "Monthly/Yearly Rentals",
            "price_range": "IDR 15M - 150M/month",
            "unique_features": [
                "Pet policies",
                "Sublease information", 
                "Monthly cost breakdowns",
                "Availability filtering"
            ],
            "property_count": "49 URLs",
            "target_market": "Digital Nomads, Expats"
        },
        "betterplace": {
            "focus": "Investment Properties",
            "price_range": "USD 120K - 2M+",
            "unique_features": [
                "Leasehold/Freehold distinction",
                "ROI calculations",
                "Investment potential",
                "Lease expiry dates",
                "Price per sqm"
            ],
            "property_count": "1378+ listings",
            "target_market": "Property Investors"
        },
        "bali_villa_realty": {
            "focus": "Premium Furnished Rentals",
            "price_range": "USD 1.5K - 10K/month",
            "unique_features": [
                "Multi-currency pricing (USD/IDR)",
                "Monthly & yearly options",
                "Fully furnished properties",
                "Premium locations",
                "Professional management"
            ],
            "property_count": "200+ premium villas",
            "target_market": "Premium Renters, Luxury Travelers"
        }
    }
    
    for source, details in comparison.items():
        print(f"\n📊 {source.upper().replace('_', ' ')}")
        print(f"   Focus: {details['focus']}")
        print(f"   Price Range: {details['price_range']}")
        print(f"   Target Market: {details['target_market']}")
        print(f"   Property Count: {details['property_count']}")
        print(f"   Unique Features:")
        for feature in details['unique_features']:
            print(f"     • {feature}")

def demo_unified_coverage():
    """Show complete market coverage"""
    print("\n🌍 COMPLETE BALI MARKET COVERAGE")
    print("=" * 50)
    
    market_coverage = {
        "rental_market": {
            "budget_rentals": "Bali Home Immo (IDR 15M-150M/month)",
            "premium_rentals": "Bali Villa Realty (USD 1.5K-10K/month)",
            "coverage": "Complete rental spectrum from budget to luxury"
        },
        "investment_market": {
            "property_sales": "BetterPlace (USD 120K-2M+)",
            "ownership_types": "Leasehold & Freehold options",
            "coverage": "Complete investment property market"
        },
        "geographic_coverage": {
            "popular_areas": ["Canggu", "Seminyak", "Ubud", "Kedungu", "Jimbaran"],
            "emerging_areas": ["Padonan", "Babakan", "Pandawa", "Uluwatu"],
            "coverage": "All major Bali property hotspots"
        },
        "total_properties": {
            "bali_home_immo": 49,
            "betterplace": 1378,
            "bali_villa_realty": 200,
            "total": 1627,
            "coverage": "Complete Bali property ecosystem"
        }
    }
    
    print("🏠 RENTAL MARKET:")
    print(f"   Budget: {market_coverage['rental_market']['budget_rentals']}")
    print(f"   Premium: {market_coverage['rental_market']['premium_rentals']}")
    
    print("\n🏢 INVESTMENT MARKET:")
    print(f"   Sales: {market_coverage['investment_market']['property_sales']}")
    print(f"   Types: {market_coverage['investment_market']['ownership_types']}")
    
    print("\n📍 GEOGRAPHIC COVERAGE:")
    print(f"   Popular: {', '.join(market_coverage['geographic_coverage']['popular_areas'])}")
    print(f"   Emerging: {', '.join(market_coverage['geographic_coverage']['emerging_areas'])}")
    
    print(f"\n📊 TOTAL PROPERTIES: {market_coverage['total_properties']['total']}")
    for source, count in market_coverage['total_properties'].items():
        if source != 'total' and source != 'coverage':
            print(f"   {source.replace('_', ' ').title()}: {count}")

def demo_api_capabilities():
    """Show unified API capabilities"""
    print("\n🌐 UNIFIED API CAPABILITIES")
    print("=" * 50)
    
    api_example = {
        "endpoint": "/api/properties",
        "total_results": 1627,
        "sources": ["bali_home_immo", "betterplace", "bali_villa_realty"],
        "filters": {
            "location": ["Canggu", "Seminyak", "Kedungu"],
            "property_type": ["Villa", "Apartment", "Land"],
            "price_range": {"min_usd": 1000, "max_usd": 500000},
            "bedrooms": [1, 2, 3, 4, 5],
            "rental_type": ["monthly", "yearly", "purchase"],
            "source": ["bali_home_immo", "betterplace", "bali_villa_realty"]
        },
        "sample_response": {
            "properties": [
                {
                    "id": 1,
                    "title": "Modern 3 Bedroom Villa...",
                    "price": {"usd": 299000, "type": "purchase"},
                    "source": "betterplace",
                    "highlights": ["Leasehold", "ROI Available"]
                },
                {
                    "id": 2,
                    "title": "Cozy Villa 2 Bedrooms...",
                    "price": {"usd": 2885, "type": "monthly"},
                    "source": "bali_villa_realty",
                    "highlights": ["Fully Furnished", "Multi-Currency"]
                }
            ]
        }
    }
    
    print(f"✅ ENDPOINT: {api_example['endpoint']}")
    print(f"✅ TOTAL RESULTS: {api_example['total_results']} properties")
    print(f"✅ SOURCES: {len(api_example['sources'])} integrated")
    print(f"✅ FILTERS: {len(api_example['filters'])} filter types")
    print(f"✅ CROSS-SOURCE SEARCH: Single API for all properties")
    print(f"✅ UNIFIED RESPONSE: Consistent format across sources")

def save_three_source_data(bali_property, betterplace_property, bvr_property):
    """Save demo data from all three sources"""
    Path("data/raw").mkdir(parents=True, exist_ok=True)
    
    three_source_data = {
        "analysis_date": "2025-01-08",
        "total_sources": 3,
        "total_properties": 1627,
        "sources": {
            "bali_home_immo": bali_property,
            "betterplace": betterplace_property,
            "bali_villa_realty": bvr_property
        },
        "market_coverage": {
            "rental_budget": "Bali Home Immo",
            "rental_premium": "Bali Villa Realty", 
            "investment": "BetterPlace",
            "complete_ecosystem": True
        },
        "unified_capabilities": {
            "cross_source_search": True,
            "multi_currency_support": True,
            "comprehensive_filters": True,
            "single_api_endpoint": True
        }
    }
    
    filename = "data/raw/three_source_complete.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(three_source_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Three-source data saved to: {filename}")

def main():
    """Run the complete three-source demo"""
    print("🌟 THREE-SOURCE BALI PROPERTY ECOSYSTEM")
    print("=" * 60)
    
    # Demo all three sources
    bali_property = demo_bali_home_immo()
    betterplace_property = demo_betterplace()
    bvr_property = demo_bali_villa_realty()
    
    # Show comparison
    demo_three_source_comparison()
    
    # Show market coverage
    demo_unified_coverage()
    
    # Show API capabilities
    demo_api_capabilities()
    
    # Save demo data
    save_three_source_data(bali_property, betterplace_property, bvr_property)
    
    print("\n" + "=" * 60)
    print("🎉 THREE-SOURCE ECOSYSTEM COMPLETE!")
    print("=" * 60)
    print("COMPLETE BALI PROPERTY MARKET COVERAGE:")
    print("1. 🏠 Bali Home Immo (Budget Rentals) - 49 properties")
    print("2. 🏢 BetterPlace (Investments) - 1378+ properties")
    print("3. 🏖️ Bali Villa Realty (Premium Rentals) - 200+ properties")
    print("4. 🔗 Unified API with cross-source search")
    print("5. 💱 Multi-currency support (IDR/USD/AUD/EUR)")
    print("6. 📊 Complete market spectrum coverage")
    print("\nTOTAL: 1627+ properties across all market segments!")
    print("\nTo test all sources:")
    print("  python demo_three_sources.py")
    print("  cd src/data_ingestion && python ingestion_service.py ingest --source bali_villa_realty --limit 2")

if __name__ == "__main__":
    main()
