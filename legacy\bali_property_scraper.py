#!/usr/bin/env python3
"""
Bali Home Immo Property Scraper
Scrapes all rental properties from bali-home-immo.com using Firecrawl MCP server
"""

import json
import time
import requests
from typing import List, Dict, Any
from urllib.parse import urljoin, urlparse
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BaliPropertyScraper:
    def __init__(self):
        self.base_url = "https://bali-home-immo.com"
        self.rental_url = "https://bali-home-immo.com/realestate-property/for-rent"
        self.properties = []
        
    def get_all_property_urls(self) -> List[str]:
        """
        Get all property URLs from the rental page using Firecrawl map
        """
        logger.info("Fetching all property URLs...")
        
        # This would be called via MCP server in practice
        # For now, we'll use the URLs we discovered earlier
        property_urls = [
            "https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/2-bedroom-townhouse-for-rent-in-berawa-rf1508",
            "https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/1-bedroom-pool-villa-for-monthly-rental-in-canggu-batu-bolong-bhi620-a",
            "https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/seseh/2-bedroom-villa-with-rooftop-for-monthly-rental-near-cemagi-beach-rf7743",
            "https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/2-bedroom-villa-for-monthly-yearly-rental-in-canggu-bhi1080",
            "https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/kerobokan/exqusite-3-bedroom-villa-for-monthly-rental-in-bali-kerobokan-aa061",
            # Add more URLs as needed
        ]
        
        logger.info(f"Found {len(property_urls)} property URLs")
        return property_urls
    
    def extract_property_data(self, url: str) -> Dict[str, Any]:
        """
        Extract property data from a single property URL using Firecrawl extract
        """
        logger.info(f"Extracting data from: {url}")
        
        # Define the schema for property data extraction
        schema = {
            "type": "object",
            "properties": {
                "title": {"type": "string"},
                "price": {"type": "string"},
                "payment_term": {"type": "string"},
                "location": {"type": "string"},
                "bedrooms": {"type": "integer"},
                "bathrooms": {"type": "integer"},
                "description": {"type": "string"},
                "amenities": {"type": "array", "items": {"type": "string"}},
                "size": {
                    "type": "object",
                    "properties": {
                        "land_size_sqm": {"type": "integer"},
                        "building_size_sqm": {"type": "integer"}
                    }
                },
                "year_built": {"type": "integer"},
                "property_type": {"type": "string"},
                "property_id": {"type": "string"},
                "status": {"type": "string"},
                "furnishing": {"type": "string"},
                "road_access": {"type": "string"},
                "view": {"type": "string"},
                "imb_status": {"type": "string"},
                "ownership": {"type": "string"},
                "lease_duration": {"type": "string"},
                "zoning": {"type": "string"},
                "floor_level": {"type": "integer"},
                "internet": {"type": "string"},
                "electricity_capacity": {"type": "string"},
                "water_source": {"type": "string"},
                "parking": {"type": "string"},
                "facility_layout": {"type": "string"},
                "brochure_url": {"type": "string"},
                "detail_url": {"type": "string"}
            },
            "required": ["title", "price", "location", "bedrooms", "bathrooms", "property_id", "detail_url"]
        }
        
        # This would be called via MCP server in practice
        # For demonstration, we'll return a sample structure
        extracted_data = {
            "title": "Sample Property Title",
            "price": "50.000.000 IDR / month",
            "payment_term": "Monthly",
            "location": "Sample Location",
            "bedrooms": 2,
            "bathrooms": 2,
            "description": "Sample description",
            "amenities": ["Private Pool", "Fully Furnished", "Air Conditioning"],
            "size": {
                "land_size_sqm": 200,
                "building_size_sqm": 120
            },
            "year_built": 2021,
            "property_type": "Villa",
            "property_id": "SAMPLE001",
            "status": "Available Now",
            "furnishing": "Fully Furnished",
            "view": "Garden",
            "internet": "Fiber Optic",
            "electricity_capacity": "5.500 Watt",
            "water_source": "Drilled Well",
            "parking": "Carport",
            "facility_layout": "Enclosed Living",
            "detail_url": url
        }
        
        return extracted_data
    
    def scrape_all_properties(self) -> List[Dict[str, Any]]:
        """
        Scrape all properties from the rental page
        """
        logger.info("Starting property scraping...")
        
        # Get all property URLs
        property_urls = self.get_all_property_urls()
        
        # Extract data from each property
        for i, url in enumerate(property_urls, 1):
            try:
                logger.info(f"Processing property {i}/{len(property_urls)}")
                property_data = self.extract_property_data(url)
                self.properties.append(property_data)
                
                # Add delay to be respectful to the server
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error processing {url}: {str(e)}")
                continue
        
        logger.info(f"Successfully scraped {len(self.properties)} properties")
        return self.properties
    
    def save_to_json(self, filename: str = "bali_properties.json"):
        """
        Save scraped properties to JSON file
        """
        logger.info(f"Saving {len(self.properties)} properties to {filename}")
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.properties, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Data saved successfully to {filename}")

def main():
    """
    Main function to run the scraper
    """
    scraper = BaliPropertyScraper()
    
    # Scrape all properties
    properties = scraper.scrape_all_properties()
    
    # Save to JSON file
    scraper.save_to_json()
    
    print(f"\nScraping completed! Found {len(properties)} properties.")
    print("Data saved to 'bali_properties.json'")

if __name__ == "__main__":
    main()
