#!/usr/bin/env python3
"""
Enhanced API Server with OpenAI Integration
Full AI-powered property search with GPT-4 and function calling
"""

import os
import json
import logging
from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional
import openai
from openai import OpenAI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Enhanced Bali Property Search API",
    description="AI-powered property search with OpenAI GPT-4 integration",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# OpenAI client
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
client = OpenAI(api_key=OPENAI_API_KEY)

# Sample properties database
SAMPLE_PROPERTIES = [
    {
        "id": "1",
        "title": "Cozy 2 Bedroom Villa in Canggu",
        "location": "Canggu",
        "bedrooms": 2,
        "bathrooms": 2,
        "price": {"display": 25000000, "currency": "IDR", "type": "rental"},
        "property_type": "Villa",
        "source": "bali_home_immo",
        "images": ["image1.jpg", "image2.jpg"],
        "highlights": ["Swimming Pool", "Pet Friendly"],
        "detail_url": "https://example.com/property/1",
        "description": "Beautiful 2-bedroom villa in the heart of Canggu with private pool and garden."
    },
    {
        "id": "2", 
        "title": "Modern 3 Bedroom Villa with Pool",
        "location": "Seminyak",
        "bedrooms": 3,
        "bathrooms": 3,
        "price": {"display": 3500, "currency": "USD", "type": "rental"},
        "property_type": "Villa",
        "source": "bali_villa_realty",
        "images": ["image3.jpg", "image4.jpg"],
        "highlights": ["Swimming Pool", "Furnished", "Modern"],
        "detail_url": "https://example.com/property/2",
        "description": "Stunning modern villa in Seminyak with contemporary design and luxury amenities."
    },
    {
        "id": "3",
        "title": "Investment Property in Kedungu",
        "location": "Kedungu", 
        "bedrooms": 2,
        "bathrooms": 2,
        "price": {"display": 250000, "currency": "USD", "type": "purchase"},
        "property_type": "Villa",
        "source": "betterplace",
        "images": ["image5.jpg", "image6.jpg"],
        "highlights": ["Investment", "Leasehold", "Beach Access"],
        "detail_url": "https://example.com/property/3",
        "description": "Excellent investment opportunity in emerging Kedungu area with beach access."
    },
    {
        "id": "4",
        "title": "Pet-Friendly Apartment in Ubud",
        "location": "Ubud",
        "bedrooms": 1,
        "bathrooms": 1,
        "price": {"display": 15000000, "currency": "IDR", "type": "rental"},
        "property_type": "Apartment",
        "source": "bali_home_immo",
        "images": ["image7.jpg"],
        "highlights": ["Pet Friendly", "Garden View"],
        "detail_url": "https://example.com/property/4",
        "description": "Charming apartment in peaceful Ubud with garden views, perfect for pet owners."
    },
    {
        "id": "5",
        "title": "Luxury 4 Bedroom Villa with Pool",
        "location": "Jimbaran",
        "bedrooms": 4,
        "bathrooms": 4,
        "price": {"display": 8500, "currency": "USD", "type": "rental"},
        "property_type": "Villa",
        "source": "bali_villa_realty",
        "images": ["image8.jpg", "image9.jpg", "image10.jpg"],
        "highlights": ["Swimming Pool", "Luxury", "Ocean View"],
        "detail_url": "https://example.com/property/5",
        "description": "Luxurious 4-bedroom villa in Jimbaran with stunning ocean views and private pool."
    }
]

# Conversation sessions
conversation_sessions = {}

class ChatRequest(BaseModel):
    """Chat request model"""
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    """Chat response model"""
    success: bool
    response: str
    session_id: str
    handoff_triggered: bool = False

def get_system_prompt():
    """Get the system prompt for the AI assistant"""
    return """You are a helpful, friendly property assistant specialized in long-term rentals and real estate in Bali. Your task is to help users find a home by asking conversational, human questions about what they're looking for, such as location, price range, property type, number of bedrooms, bathrooms, desired start date and lease duration.

Always use the `search_properties` function to perform a search once you have enough information. Never invent results. Show a maximum of 3 listings at a time, and offer users the option to refine or broaden their filters. If you're unsure about their request, ask clarifying questions. Keep your tone supportive and to the point.

Available locations in Bali include: Canggu, Seminyak, Ubud, Kedungu, Jimbaran, and more.

Property types available: Villa, Apartment, House, Land.

Price ranges vary from IDR 15M-150M per month for rentals, and USD 120K-2M+ for purchases.

You have access to properties from three sources:
1. Bali Home Immo - Budget rentals with pet policies and cost breakdowns
2. BetterPlace - Investment properties with ROI data and leasehold information  
3. Bali Villa Realty - Premium furnished rentals with multi-currency pricing

Always ask clarifying questions if the user's request is unclear, and summarize their preferences before searching. When you find suitable properties, offer to help them contact the property owners or agents."""

def get_search_function():
    """Get the search function definition for OpenAI"""
    return {
        "name": "search_properties",
        "description": "Search for properties in Bali based on user preferences",
        "parameters": {
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "Location/area in Bali (e.g., Canggu, Seminyak, Ubud)"
                },
                "bedrooms": {
                    "type": "integer",
                    "description": "Number of bedrooms",
                    "minimum": 1,
                    "maximum": 10
                },
                "bathrooms": {
                    "type": "integer",
                    "description": "Number of bathrooms",
                    "minimum": 1,
                    "maximum": 10
                },
                "max_price": {
                    "type": "integer",
                    "description": "Maximum price in IDR for rentals or USD for purchases"
                },
                "property_type": {
                    "type": "string",
                    "description": "Type of property",
                    "enum": ["Villa", "Apartment", "House", "Land"]
                },
                "term": {
                    "type": "string",
                    "description": "Rental term or purchase",
                    "enum": ["monthly", "yearly", "purchase"]
                },
                "pool": {
                    "type": "boolean",
                    "description": "Whether property should have a swimming pool"
                },
                "pet_friendly": {
                    "type": "boolean",
                    "description": "Whether property should be pet-friendly"
                }
            },
            "required": []
        }
    }

def search_properties_function(**kwargs):
    """Function to search properties (called by OpenAI)"""
    try:
        logger.info(f"Searching properties with params: {kwargs}")
        
        # Filter properties based on parameters
        filtered_properties = []
        
        for prop in SAMPLE_PROPERTIES:
            # Apply location filter
            if kwargs.get('location') and kwargs['location'].lower() not in prop["location"].lower():
                continue
            
            # Apply bedroom filter
            if kwargs.get('bedrooms') and prop["bedrooms"] != kwargs['bedrooms']:
                continue
            
            # Apply bathroom filter
            if kwargs.get('bathrooms') and prop["bathrooms"] != kwargs['bathrooms']:
                continue
            
            # Apply property type filter
            if kwargs.get('property_type') and prop["property_type"] != kwargs['property_type']:
                continue
            
            # Apply term filter
            if kwargs.get('term'):
                if kwargs['term'] == "purchase" and prop["price"]["type"] != "purchase":
                    continue
                elif kwargs['term'] in ["monthly", "yearly"] and prop["price"]["type"] != "rental":
                    continue
            
            # Apply pool filter
            if kwargs.get('pool') and "Swimming Pool" not in prop["highlights"]:
                continue
            
            # Apply pet friendly filter
            if kwargs.get('pet_friendly') and "Pet Friendly" not in prop["highlights"]:
                continue
            
            # Apply price filter (simplified)
            if kwargs.get('max_price'):
                prop_price = prop["price"]["display"]
                if prop["price"]["currency"] == "USD" and kwargs['max_price'] > 1000:
                    # Assume IDR if max_price is large
                    prop_price = prop_price * 16000
                elif prop["price"]["currency"] == "IDR" and kwargs['max_price'] < 1000:
                    # Assume USD if max_price is small
                    prop_price = prop_price / 16000
                
                if prop_price > kwargs['max_price']:
                    continue
            
            filtered_properties.append(prop)
        
        # Format results for AI
        results = {
            "total": len(filtered_properties),
            "properties": filtered_properties[:3],  # Max 3 as per system prompt
            "search_params": kwargs
        }
        
        logger.info(f"Found {len(filtered_properties)} properties")
        return results
        
    except Exception as e:
        logger.error(f"Search error: {str(e)}")
        return {"total": 0, "properties": [], "error": str(e)}

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Enhanced Bali Property Search API with OpenAI",
        "version": "2.0.0",
        "status": "active",
        "ai_enabled": True,
        "sources": ["bali_home_immo", "betterplace", "bali_villa_realty"],
        "total_properties": len(SAMPLE_PROPERTIES)
    }

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Enhanced chat endpoint with OpenAI GPT-4"""
    try:
        # Get or create session
        session_id = request.session_id or f"session_{len(conversation_sessions) + 1}"
        
        if session_id not in conversation_sessions:
            conversation_sessions[session_id] = [
                {"role": "system", "content": get_system_prompt()}
            ]
        
        # Add user message
        conversation_sessions[session_id].append({
            "role": "user", 
            "content": request.message
        })
        
        # Call OpenAI with function calling
        response = client.chat.completions.create(
            model="gpt-4-turbo-preview",
            messages=conversation_sessions[session_id],
            functions=[get_search_function()],
            function_call="auto",
            temperature=0.7,
            max_tokens=1000
        )
        
        message = response.choices[0].message
        
        # Check if function was called
        if message.function_call:
            function_name = message.function_call.name
            function_args = json.loads(message.function_call.arguments)
            
            logger.info(f"Function called: {function_name} with args: {function_args}")
            
            # Add function call to conversation
            conversation_sessions[session_id].append({
                "role": "assistant",
                "content": message.content,
                "function_call": {
                    "name": function_name,
                    "arguments": message.function_call.arguments
                }
            })
            
            # Execute function
            if function_name == "search_properties":
                search_results = search_properties_function(**function_args)
                
                # Add function result to conversation
                conversation_sessions[session_id].append({
                    "role": "function",
                    "name": function_name,
                    "content": json.dumps(search_results)
                })
                
                # Get final response
                final_response = client.chat.completions.create(
                    model="gpt-4-turbo-preview",
                    messages=conversation_sessions[session_id],
                    temperature=0.7,
                    max_tokens=1500
                )
                
                final_content = final_response.choices[0].message.content
                conversation_sessions[session_id].append({
                    "role": "assistant",
                    "content": final_content
                })
                
                # Check for handoff triggers
                handoff_triggered = any(word in request.message.lower() for word in 
                                     ['contact', 'call', 'speak', 'agent', 'interested'])
                
                return ChatResponse(
                    success=True,
                    response=final_content,
                    session_id=session_id,
                    handoff_triggered=handoff_triggered
                )
        
        else:
            # No function call, regular response
            content = message.content
            conversation_sessions[session_id].append({
                "role": "assistant",
                "content": content
            })
            
            return ChatResponse(
                success=True,
                response=content,
                session_id=session_id,
                handoff_triggered=False
            )
            
    except Exception as e:
        logger.error(f"Chat error: {str(e)}")
        return ChatResponse(
            success=False,
            response=f"I'm sorry, I encountered an error: {str(e)}",
            session_id=session_id or "error_session",
            handoff_triggered=False
        )

@app.post("/api/transcribe")
async def transcribe_audio(audio_file: UploadFile = File(...)):
    """Audio transcription endpoint using Whisper"""
    try:
        # Validate file format
        if not audio_file.filename.lower().endswith(('.mp3', '.wav', '.m4a', '.webm')):
            raise HTTPException(status_code=400, detail="Unsupported audio format")
        
        # Read audio data
        audio_data = await audio_file.read()
        
        # Create temporary file for Whisper
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_file.write(audio_data)
            temp_file.flush()
            
            # Transcribe with Whisper
            with open(temp_file.name, 'rb') as audio:
                transcript = client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio
                )
            
            # Clean up temp file
            os.unlink(temp_file.name)
            
            return {
                "success": True,
                "transcription": transcript.text,
                "language": getattr(transcript, 'language', None)
            }
            
    except Exception as e:
        logger.error(f"Transcription error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Transcription failed: {str(e)}")

# Include all other endpoints from simple_api.py
@app.get("/api/stats")
async def get_stats():
    """Get overall statistics"""
    return {
        "total_listings": len(SAMPLE_PROPERTIES),
        "available_listings": len(SAMPLE_PROPERTIES),
        "sources": {
            "bali_home_immo": 2,
            "betterplace": 1,
            "bali_villa_realty": 2
        },
        "ai_enabled": True,
        "active_sessions": len(conversation_sessions)
    }

@app.get("/api/locations")
async def get_locations():
    """Get available locations"""
    locations = list(set([prop["location"] for prop in SAMPLE_PROPERTIES]))
    locations.sort()
    
    return {
        "locations": locations,
        "total": len(locations)
    }

if __name__ == "__main__":
    import uvicorn
    print("🤖 Starting Enhanced Bali Property Search API with OpenAI...")
    print("📍 Server will be available at: http://localhost:8000")
    print("🧠 AI Features: GPT-4 Chat + Whisper Transcription")
    print("📚 API Documentation: http://localhost:8000/docs")
    uvicorn.run(app, host="0.0.0.0", port=8000)
