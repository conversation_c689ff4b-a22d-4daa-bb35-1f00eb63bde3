#!/usr/bin/env python3
"""
Complete Field Coverage Demo
Shows ALL fields from both sources and unified schema analysis
"""

import json
from pathlib import Path

def demo_bali_home_immo_complete():
    """Complete Bali Home Immo field coverage"""
    print("🏠 BALI HOME IMMO - COMPLETE FIELD COVERAGE")
    print("=" * 55)
    
    complete_property = {
        # UNIFIED FIELDS (16)
        "title": "COZY 2 BEDROOMS VILLA - AD019",
        "location": "Pandawa, Kutuh", 
        "bedrooms": 2,
        "bathrooms": 2,
        "property_type": "Villa",
        "property_id": "AD019",
        "description": "This cozy 2-bedrooms villa is located in the tranquil area...",
        "images": ["url1", "url2", "url3", "url4", "url5"],
        "detail_url": "https://bali-home-immo.com/...",
        "land_size_sqm": 150,
        "building_size_sqm": 100,
        "year_built": 2024,
        "furnishing": "Furnished",
        "pool_type": "Private",
        "parking_type": "Open",
        "amenities": ["Furnished", "Air Conditioner: 2", "Internet: Fiber Optic"],
        
        # BALI HOME IMMO SPECIFIC (12)
        "payment_term": "monthly",
        "status": "Available",
        "availability_date": "01/09/2025",
        "pet_policy": "Pet-friendly",
        "sublease_allowed": True,
        "monthly_costs": {
            "banjar_fee_security": "Included",
            "cleaning_service": "Included", 
            "pool_maintenance": "Included",
            "garden_maintenance": "Included",
            "bin_collection": "Included",
            "electricity": "Not included",
            "internet_included": "Included"
        },
        "indoor_details": {
            "living_room": "Opened",
            "dining_room": "Opened", 
            "kitchen": "Opened"
        },
        "outdoor_details": {
            "swimming_pool": True,
            "pool_size": "5m x 3m",
            "garden": True,
            "terrace": True
        },
        "electricity_capacity": "4.400",
        "water_source": "PAM",
        "internet": "Fiber Optic",
        "air_conditioner_count": 2,
        
        "source": "bali_home_immo"
    }
    
    print(f"✅ UNIFIED FIELDS: 16")
    print(f"✅ SOURCE-SPECIFIC FIELDS: 12") 
    print(f"✅ TOTAL FIELDS: 28")
    print(f"✅ FOCUS: Monthly rentals, cost breakdowns, pet policies")
    
    return complete_property

def demo_betterplace_complete():
    """Complete BetterPlace field coverage"""
    print("\n🏢 BETTERPLACE - COMPLETE FIELD COVERAGE")
    print("=" * 55)
    
    complete_property = {
        # UNIFIED FIELDS (16) 
        "title": "Modern 3 Bedroom Villa with Rooftop in Pangkung Tibah",
        "location": "Kedungu",
        "bedrooms": 3,
        "bathrooms": 4,
        "property_type": "Villa",
        "property_id": "BPVL02232",
        "description": "Located in the peaceful village of Pangkung Tibah...",
        "images": ["url1", "url2", "...", "url31"],  # 31 total
        "detail_url": "https://betterplace.cc/buy/properties/BPVL02232/",
        "land_size_sqm": 260,
        "building_size_sqm": 166,
        "year_built": 2024,
        "furnishing": "Fully-Furnished",
        "pool_type": "Private",
        "parking_type": "Private Bike",
        "amenities": ["Private pool", "Enclosed livingroom", "TV", "Fully Equipped Kitchen"],
        
        # BETTERPLACE SPECIFIC (31)
        "price_idr": "IDR 4,866,049,227",
        "price_usd": "USD 299,000",
        "price_aud": "",
        "price_eur": "",
        "price_sgd": "",
        "ownership_type": "Leasehold",
        "lease_duration": "26 Years",
        "lease_expiry": "Q3 2051",
        "construction_status": "Completed",
        "managed_by": "Betterplace",
        "price_per_sqm": "IDR 29,313,550",
        "levels": 2,
        "distance_to_beach": "3 Minutes",
        "living_room_type": "Enclosed",
        "roi_available": True,
        "roi_info": "Request ROI available",
        "handover_date": "",
        "investment_potential": "Strong growth potential in Kedungu area",
        "currency_options": ["IDR", "USD", "AUD", "EUR", "SGD"],
        "total_photos": 31,
        "video_tour_url": "",
        "tour_360_url": "",
        "floor_plan_url": "",
        "location_map_details": "Interactive map available",
        "agent_whatsapp": "+62 811-3900-2007",
        "agent_email": "",
        "company_phone": "+62 813 7773 2003",
        "company_email": "<EMAIL>",
        "company_address": "Jl. Raya Semat, Canggu",
        "breadcrumb": ["Home", "Buy", "Indonesia", "Bali", "BPVL02232"],
        "similar_properties": ["BPVL00506", "BPVL01427", "BPVL01486"],
        "tabs_available": ["Details", "Description", "Features", "Video tour", "360 Tour", "Floor plan", "Location map"],
        
        "source": "betterplace"
    }
    
    print(f"✅ UNIFIED FIELDS: 16")
    print(f"✅ SOURCE-SPECIFIC FIELDS: 31")
    print(f"✅ TOTAL FIELDS: 47") 
    print(f"✅ FOCUS: Investment properties, leasehold/freehold, ROI data")
    
    return complete_property

def demo_unified_schema():
    """Show the unified schema approach"""
    print("\n🔗 UNIFIED SCHEMA APPROACH")
    print("=" * 55)
    
    unified_example = {
        # CORE UNIFIED (Always present)
        "id": 1,
        "title": "Modern 3 Bedroom Villa...",
        "location": "Kedungu",
        "bedrooms": 3,
        "bathrooms": 4,
        "property_type": "Villa",
        "property_id": "BPVL02232",
        "description": "Located in the peaceful village...",
        "images": ["url1", "url2", "url3"],
        "detail_url": "https://betterplace.cc/...",
        "source": "betterplace",
        
        # EXTENDED UNIFIED (Often present)
        "land_size_sqm": 260,
        "building_size_sqm": 166,
        "year_built": 2024,
        "furnishing": "Fully-Furnished",
        "pool_type": "Private",
        "parking_type": "Private Bike",
        "amenities": ["Private pool", "Enclosed livingroom"],
        
        # PRICE (Normalized)
        "price": {
            "display": "IDR 4,866,049,227",
            "usd": "299,000",
            "currency": "IDR",
            "per_sqm": "IDR 29,313,550"
        },
        
        # SOURCE-SPECIFIC DATA (JSON)
        "source_specific": {
            "ownership_type": "Leasehold",
            "lease_duration": "26 Years", 
            "lease_expiry": "Q3 2051",
            "distance_to_beach": "3 Minutes",
            "roi_available": True,
            "total_photos": 31,
            "agent_whatsapp": "+62 811-3900-2007",
            "tabs_available": ["Details", "Description", "Features"]
        },
        
        # META
        "created_at": "2025-01-08T10:00:00Z",
        "updated_at": "2025-01-08T10:00:00Z",
        "is_active": True
    }
    
    print("✅ CORE UNIFIED: 10 fields (always present)")
    print("✅ EXTENDED UNIFIED: 7 fields (often present)")
    print("✅ PRICE OBJECT: Normalized across sources")
    print("✅ SOURCE-SPECIFIC: JSON field for unique data")
    print("✅ TOTAL STRUCTURE: Scalable for any number of sources")

def demo_field_statistics():
    """Show complete field statistics"""
    print("\n📊 COMPLETE FIELD STATISTICS")
    print("=" * 55)
    
    stats = {
        "total_fields_analyzed": 59,
        "unified_fields": 16,
        "bali_home_immo_specific": 12,
        "betterplace_specific": 31,
        "unified_percentage": round(16/59 * 100, 1),
        "coverage_analysis": {
            "bali_home_immo_total": 28,
            "betterplace_total": 47,
            "richest_source": "BetterPlace (47 fields)",
            "specialization": {
                "bali_home_immo": "Rental market (monthly costs, pet policies)",
                "betterplace": "Investment market (ROI, leasehold, multi-currency)"
            }
        }
    }
    
    print(f"📈 TOTAL FIELDS ANALYZED: {stats['total_fields_analyzed']}")
    print(f"🔗 UNIFIED FIELDS: {stats['unified_fields']} ({stats['unified_percentage']}%)")
    print(f"🏠 BALI HOME IMMO SPECIFIC: {stats['bali_home_immo_specific']}")
    print(f"🏢 BETTERPLACE SPECIFIC: {stats['betterplace_specific']}")
    print(f"🏆 RICHEST SOURCE: {stats['coverage_analysis']['richest_source']}")
    
    print(f"\n🎯 SPECIALIZATION:")
    for source, spec in stats['coverage_analysis']['specialization'].items():
        print(f"   {source.upper().replace('_', ' ')}: {spec}")

def demo_api_response():
    """Show unified API response format"""
    print("\n🌐 UNIFIED API RESPONSE FORMAT")
    print("=" * 55)
    
    api_response = {
        "total": 1476,
        "sources": {
            "bali_home_immo": 49,
            "betterplace": 1427
        },
        "filters_applied": {
            "location": ["Kedungu", "Canggu"],
            "bedrooms": [2, 3],
            "max_price_usd": 500000
        },
        "properties": [
            {
                "id": 1,
                "title": "Modern 3 Bedroom Villa...",
                "location": "Kedungu",
                "bedrooms": 3,
                "price": {
                    "display": "IDR 4,866,049,227",
                    "usd": "299,000"
                },
                "source": "betterplace",
                "highlights": ["Leasehold", "3 Minutes to Beach", "ROI Available"]
            },
            {
                "id": 2, 
                "title": "Cozy 2 Bedrooms Villa...",
                "location": "Pandawa",
                "bedrooms": 2,
                "price": {
                    "display": "IDR 19,000,000/month",
                    "monthly": True
                },
                "source": "bali_home_immo",
                "highlights": ["Pet Friendly", "Pool Maintenance Included", "Available Now"]
            }
        ]
    }
    
    print("✅ CROSS-SOURCE RESULTS: Both sources in single response")
    print("✅ NORMALIZED PRICING: Consistent format across sources")
    print("✅ SOURCE IDENTIFICATION: Clear source attribution")
    print("✅ HIGHLIGHTS: Key differentiators per property")
    print("✅ FILTER SUPPORT: Cross-source filtering capabilities")

def save_complete_analysis(bali_property, betterplace_property):
    """Save complete field analysis"""
    Path("data/raw").mkdir(parents=True, exist_ok=True)
    
    complete_analysis = {
        "analysis_date": "2025-01-08",
        "total_fields_discovered": 59,
        "unified_fields": 16,
        "source_comparison": {
            "bali_home_immo": {
                "total_fields": 28,
                "unique_fields": 12,
                "focus": "Rental market",
                "sample_property": bali_property
            },
            "betterplace": {
                "total_fields": 47,
                "unique_fields": 31,
                "focus": "Investment market", 
                "sample_property": betterplace_property
            }
        },
        "recommendations": {
            "unified_schema": "Use core + extended + source_specific JSON approach",
            "database_design": "Flexible schema with JSON fields for source-specific data",
            "api_design": "Single endpoint with source filtering and normalization"
        }
    }
    
    filename = "data/raw/complete_field_analysis.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(complete_analysis, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Complete analysis saved to: {filename}")

def main():
    """Run complete field coverage demo"""
    print("🔍 COMPLETE FIELD COVERAGE ANALYSIS")
    print("=" * 60)
    
    # Demo complete field coverage
    bali_property = demo_bali_home_immo_complete()
    betterplace_property = demo_betterplace_complete()
    
    # Show unified approach
    demo_unified_schema()
    
    # Show statistics
    demo_field_statistics()
    
    # Show API format
    demo_api_response()
    
    # Save analysis
    save_complete_analysis(bali_property, betterplace_property)
    
    print("\n" + "=" * 60)
    print("🎉 COMPLETE FIELD ANALYSIS FINISHED!")
    print("=" * 60)
    print("KEY FINDINGS:")
    print("• BetterPlace has 31 unique fields (investment focus)")
    print("• Bali Home Immo has 12 unique fields (rental focus)")
    print("• Only 16 fields (27%) are truly unified")
    print("• Need flexible schema with JSON for source-specific data")
    print("• Both sources complement each other perfectly")
    print("\nNEXT: Implement unified database schema and API layer!")

if __name__ == "__main__":
    main()
