#!/usr/bin/env python3
"""
Search API for Property Listings
Provides REST endpoints for the conversational layer
Based on PRD: Conversational Layer & UX
"""

from fastapi import FastAPI, HTTPException, Query, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional
import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from data_ingestion.ingestion_service import PropertyIngestionService
from conversational.enhanced_assistant import EnhancedPropertyAssistant

app = FastAPI(
    title="Bali Property Search API",
    description="API for searching properties across multiple sources in Bali",
    version="1.0.0"
)

# Add CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize ingestion service
ingestion_service = PropertyIngestionService()

class PropertySearchRequest(BaseModel):
    """
    Search request model for property filtering
    Based on PRD function calling schema
    """
    location: Optional[str] = Field(None, description="Location/area in Bali (e.g., Canggu, Seminyak)")
    bedrooms: Optional[int] = Field(None, description="Number of bedrooms", ge=1, le=10)
    bathrooms: Optional[int] = Field(None, description="Number of bathrooms", ge=1, le=10)
    max_price: Optional[int] = Field(None, description="Maximum price in IDR", ge=0)
    min_price: Optional[int] = Field(None, description="Minimum price in IDR", ge=0)
    property_type: Optional[str] = Field(None, description="Type of property (Villa, Apartment, House)")
    term: Optional[str] = Field(None, description="Rental term (monthly, yearly, purchase)")
    source: Optional[str] = Field(None, description="Specific source (bali_home_immo, betterplace, bali_villa_realty)")
    furnished: Optional[bool] = Field(None, description="Furnished property")
    pool: Optional[bool] = Field(None, description="Has swimming pool")
    pet_friendly: Optional[bool] = Field(None, description="Pet-friendly property")
    limit: Optional[int] = Field(10, description="Maximum number of results", ge=1, le=50)

class PropertyResponse(BaseModel):
    """
    Property response model for API
    """
    id: str
    title: str
    location: str
    bedrooms: int
    bathrooms: int
    price: Dict[str, Any]
    property_type: str
    source: str
    images: List[str]
    highlights: List[str]
    detail_url: str

class SearchResponse(BaseModel):
    """
    Search response model
    """
    total: int
    properties: List[PropertyResponse]
    filters_applied: Dict[str, Any]
    sources: Dict[str, int]

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Bali Property Search API",
        "version": "1.0.0",
        "status": "active",
        "sources": ["bali_home_immo", "betterplace", "bali_villa_realty"]
    }

@app.get("/api/stats")
async def get_stats():
    """Get overall statistics"""
    try:
        stats = ingestion_service.get_ingestion_stats()
        return {
            "total_listings": stats["total_listings"],
            "available_listings": stats["available_listings"],
            "recent_crawl_sessions": len(stats["recent_crawl_sessions"]),
            "sources": ["bali_home_immo", "betterplace", "bali_villa_realty"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting stats: {str(e)}")

@app.post("/api/search", response_model=SearchResponse)
async def search_properties(request: PropertySearchRequest):
    """
    Search properties based on filters
    Main endpoint for conversational layer
    """
    try:
        # Convert request to filters
        filters = {}
        
        if request.location:
            filters['location'] = request.location
        if request.bedrooms:
            filters['min_bedrooms'] = request.bedrooms
        if request.max_price:
            filters['max_price'] = request.max_price
        if request.property_type:
            filters['property_type'] = request.property_type
        
        # Get listings from database
        listings = ingestion_service.get_available_listings(filters)
        
        # Apply additional filters
        filtered_listings = []
        for listing in listings:
            # Apply source filter
            if request.source and listing.get('source') != request.source:
                continue
                
            # Apply term filter (rental vs purchase)
            if request.term:
                if request.term == 'purchase' and listing.get('source') != 'betterplace':
                    continue
                elif request.term in ['monthly', 'yearly'] and listing.get('source') == 'betterplace':
                    continue
            
            # Apply furnished filter
            if request.furnished is not None:
                furnishing = listing.get('furnishing', '').lower()
                is_furnished = 'furnished' in furnishing
                if request.furnished != is_furnished:
                    continue
            
            # Apply pool filter
            if request.pool is not None:
                has_pool = 'pool' in str(listing.get('amenities', [])).lower()
                if request.pool != has_pool:
                    continue
            
            # Apply pet filter
            if request.pet_friendly is not None:
                pet_policy = listing.get('pet_policy', '').lower()
                is_pet_friendly = 'pet' in pet_policy and 'friendly' in pet_policy
                if request.pet_friendly != is_pet_friendly:
                    continue
            
            filtered_listings.append(listing)
        
        # Limit results
        limited_listings = filtered_listings[:request.limit]
        
        # Convert to response format
        properties = []
        for listing in limited_listings:
            # Determine price format based on source
            price_info = {}
            if listing.get('source') == 'betterplace':
                price_info = {
                    "display": listing.get('price_numeric', 0),
                    "currency": "IDR",
                    "type": "purchase"
                }
            else:
                price_info = {
                    "display": listing.get('price_numeric', 0),
                    "currency": "IDR", 
                    "type": "rental"
                }
            
            # Generate highlights
            highlights = []
            if listing.get('pet_policy') and 'friendly' in listing.get('pet_policy', '').lower():
                highlights.append("Pet Friendly")
            if 'pool' in str(listing.get('amenities', [])).lower():
                highlights.append("Swimming Pool")
            if listing.get('furnishing') and 'furnished' in listing.get('furnishing', '').lower():
                highlights.append("Furnished")
            if listing.get('source') == 'betterplace':
                highlights.append("Investment Property")
            
            property_response = PropertyResponse(
                id=str(listing.get('id', '')),
                title=listing.get('title', ''),
                location=listing.get('location', ''),
                bedrooms=listing.get('bedrooms', 0),
                bathrooms=listing.get('bathrooms', 0),
                price=price_info,
                property_type=listing.get('property_type', ''),
                source=listing.get('source', ''),
                images=listing.get('images', [])[:3],  # Limit to 3 images
                highlights=highlights,
                detail_url=listing.get('detail_url', '')
            )
            properties.append(property_response)
        
        # Count by source
        sources = {}
        for listing in filtered_listings:
            source = listing.get('source', 'unknown')
            sources[source] = sources.get(source, 0) + 1
        
        return SearchResponse(
            total=len(filtered_listings),
            properties=properties,
            filters_applied=request.dict(exclude_none=True),
            sources=sources
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search error: {str(e)}")

@app.get("/api/property/{property_id}")
async def get_property_details(property_id: str):
    """Get detailed information for a specific property"""
    try:
        # This would fetch from database by ID
        # For now, return placeholder
        return {
            "id": property_id,
            "message": "Property details endpoint - to be implemented",
            "note": "This would return full property details including all images, contact info, etc."
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting property: {str(e)}")

@app.get("/api/locations")
async def get_locations():
    """Get available locations for filtering"""
    try:
        # Get unique locations from database
        listings = ingestion_service.get_available_listings()
        locations = list(set([listing.get('location', '') for listing in listings if listing.get('location')]))
        locations.sort()
        
        return {
            "locations": locations,
            "total": len(locations)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting locations: {str(e)}")

# Enhanced Assistant Integration
enhanced_assistant = None

def get_enhanced_assistant():
    """Get or create enhanced assistant instance"""
    global enhanced_assistant
    if enhanced_assistant is None:
        openai_key = os.getenv('OPENAI_API_KEY')
        slack_webhook = os.getenv('SLACK_WEBHOOK_URL')
        if openai_key:
            enhanced_assistant = EnhancedPropertyAssistant(
                openai_api_key=openai_key,
                slack_webhook_url=slack_webhook
            )
    return enhanced_assistant

@app.post("/api/chat")
async def chat_endpoint(request: Dict[str, Any]):
    """
    Enhanced chat endpoint with OpenAI integration
    """
    try:
        assistant = get_enhanced_assistant()
        if not assistant:
            raise HTTPException(status_code=500, detail="OpenAI API key not configured")

        message = request.get('message', '')
        session_id = request.get('session_id')

        if not message:
            raise HTTPException(status_code=400, detail="Message is required")

        # Start or resume conversation
        if session_id:
            await assistant.start_conversation(session_id=session_id)
        else:
            session_id = await assistant.start_conversation()

        # Process message
        result = await assistant.process_text_message(message)

        return {
            "success": result["success"],
            "response": result["response"],
            "session_id": result["session_id"],
            "handoff_triggered": result.get("handoff_triggered", False)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chat error: {str(e)}")

@app.post("/api/transcribe")
async def transcribe_audio(audio_file: UploadFile = File(...)):
    """
    Audio transcription endpoint using Whisper
    """
    try:
        assistant = get_enhanced_assistant()
        if not assistant:
            raise HTTPException(status_code=500, detail="OpenAI API key not configured")

        # Validate file format
        if not audio_file.filename.lower().endswith(('.mp3', '.wav', '.m4a', '.webm')):
            raise HTTPException(status_code=400, detail="Unsupported audio format")

        # Read audio data
        audio_data = await audio_file.read()

        # Transcribe
        result = await assistant.voice_interface.whisper.transcribe_audio(audio_data)

        if result["success"]:
            return {
                "success": True,
                "transcription": result["text"],
                "language": result.get("language"),
                "confidence": result.get("confidence")
            }
        else:
            raise HTTPException(status_code=500, detail=f"Transcription failed: {result.get('error')}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Audio transcription error: {str(e)}")

@app.post("/api/voice-chat")
async def voice_chat_endpoint(audio_file: UploadFile = File(...), session_id: str = None):
    """
    Complete voice chat endpoint (transcribe + process + respond)
    """
    try:
        assistant = get_enhanced_assistant()
        if not assistant:
            raise HTTPException(status_code=500, detail="OpenAI API key not configured")

        # Validate file format
        if not audio_file.filename.lower().endswith(('.mp3', '.wav', '.m4a', '.webm')):
            raise HTTPException(status_code=400, detail="Unsupported audio format")

        # Read audio data
        audio_data = await audio_file.read()

        # Start or resume conversation
        if session_id:
            await assistant.start_conversation(session_id=session_id)
        else:
            session_id = await assistant.start_conversation()

        # Process voice message
        result = await assistant.process_voice_message(audio_data)

        return {
            "success": result["success"],
            "transcription": result.get("transcription", ""),
            "response": result.get("response", ""),
            "session_id": result.get("session_id"),
            "handoff_triggered": result.get("handoff_triggered", False),
            "language": result.get("language"),
            "confidence": result.get("confidence")
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Voice chat error: {str(e)}")

@app.post("/api/contact")
async def set_contact_info(request: Dict[str, Any]):
    """
    Set contact information for session
    """
    try:
        assistant = get_enhanced_assistant()
        if not assistant:
            raise HTTPException(status_code=500, detail="Enhanced assistant not available")

        contact_info = request.get('contact_info', '')
        session_id = request.get('session_id')

        if not contact_info or not session_id:
            raise HTTPException(status_code=400, detail="Contact info and session ID are required")

        # Set contact info
        success = await assistant.set_contact_info(contact_info)

        return {
            "success": success,
            "message": "Contact information saved" if success else "Failed to save contact information"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Contact info error: {str(e)}")

@app.get("/api/session/{session_id}")
async def get_session_info(session_id: str):
    """
    Get session information and summary
    """
    try:
        assistant = get_enhanced_assistant()
        if not assistant:
            raise HTTPException(status_code=500, detail="Enhanced assistant not available")

        # Start session to load it
        await assistant.start_conversation(session_id=session_id)

        # Get session summary
        summary = await assistant.get_session_summary()

        if not summary:
            raise HTTPException(status_code=404, detail="Session not found")

        return summary

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Session info error: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
