#!/usr/bin/env python3
"""
Batch Property Scraper for Bali Home Immo
Processes properties in batches and saves results
"""

import json
import time
from property_urls import PROPERTY_URLS

def save_batch_results(batch_data, batch_num):
    """Save batch results to individual files"""
    filename = f"batch_{batch_num:02d}_properties.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(batch_data, f, indent=2, ensure_ascii=False)
    print(f"Saved batch {batch_num} to {filename}")

def combine_all_batches():
    """Combine all batch files into one complete dataset"""
    all_properties = []
    batch_num = 1
    
    while True:
        filename = f"batch_{batch_num:02d}_properties.json"
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                batch_data = json.load(f)
                if isinstance(batch_data, list):
                    all_properties.extend(batch_data)
                else:
                    all_properties.append(batch_data)
            batch_num += 1
        except FileNotFoundError:
            break
    
    # Save combined results
    with open('all_bali_properties.json', 'w', encoding='utf-8') as f:
        json.dump(all_properties, f, indent=2, ensure_ascii=False)
    
    print(f"Combined {len(all_properties)} properties from {batch_num-1} batches")
    return all_properties

def print_batch_urls():
    """Print URLs in batches for manual processing"""
    batch_size = 3
    total_batches = (len(PROPERTY_URLS) + batch_size - 1) // batch_size
    
    print(f"Total URLs: {len(PROPERTY_URLS)}")
    print(f"Batch size: {batch_size}")
    print(f"Total batches: {total_batches}")
    print("\n" + "="*80)
    
    for i in range(0, len(PROPERTY_URLS), batch_size):
        batch_num = i // batch_size + 1
        batch_urls = PROPERTY_URLS[i:i + batch_size]
        
        print(f"\nBATCH {batch_num}:")
        print(f"URLs: {batch_urls}")
        print(f"firecrawl_extract_mcp-server-firecrawl command:")
        print(f'  urls: {json.dumps(batch_urls)}')
        print(f'  prompt: "Extract comprehensive property details including title, price, payment terms, location, bedrooms, bathrooms, description, amenities, size information, year built, property type, property ID, status, furnishing, and all other relevant property details"')
        print(f'  schema: {json.dumps(get_property_schema())}')
        print("-" * 80)

def get_property_schema():
    """Get the complete property extraction schema"""
    return {
        "type": "object",
        "properties": {
            "title": {"type": "string"},
            "price": {"type": "string"},
            "payment_term": {"type": "string"},
            "location": {"type": "string"},
            "bedrooms": {"type": "integer"},
            "bathrooms": {"type": "integer"},
            "ensuite_bathrooms": {"type": "integer"},
            "description": {"type": "string"},
            "amenities": {"type": "array", "items": {"type": "string"}},
            "size": {
                "type": "object",
                "properties": {
                    "land_size_sqm": {"type": "integer"},
                    "building_size_sqm": {"type": "integer"}
                }
            },
            "year_built": {"type": "integer"},
            "property_type": {"type": "string"},
            "property_id": {"type": "string"},
            "status": {"type": "string"},
            "availability_date": {"type": "string"},
            "furnishing": {"type": "string"},
            "view": {"type": "string"},
            "style_design": {"type": "string"},
            "surrounding": {"type": "string"},
            "floor_level": {"type": "integer"},
            "electricity_capacity": {"type": "string"},
            "water_source": {"type": "string"},
            "parking": {"type": "string"},
            "internet": {"type": "string"},
            "air_conditioner_count": {"type": "integer"},
            "pet_policy": {"type": "string"},
            "sublease_allowed": {"type": "boolean"},
            "indoor_details": {
                "type": "object",
                "properties": {
                    "living_room": {"type": "string"},
                    "dining_room": {"type": "string"},
                    "kitchen": {"type": "string"}
                }
            },
            "outdoor_details": {
                "type": "object",
                "properties": {
                    "swimming_pool": {"type": "boolean"},
                    "pool_size": {"type": "string"},
                    "garden": {"type": "boolean"},
                    "terrace": {"type": "boolean"}
                }
            },
            "monthly_costs": {
                "type": "object",
                "properties": {
                    "banjar_fee_security": {"type": "string"},
                    "cleaning_service": {"type": "string"},
                    "pool_maintenance": {"type": "string"},
                    "garden_maintenance": {"type": "string"},
                    "bin_collection": {"type": "string"},
                    "electricity": {"type": "string"},
                    "internet_included": {"type": "string"}
                }
            },
            "detail_url": {"type": "string"}
        },
        "required": ["title", "price", "location", "bedrooms", "bathrooms", "property_id", "detail_url"]
    }

def generate_summary_report(properties):
    """Generate a summary report of scraped properties"""
    if not properties:
        print("No properties to analyze")
        return
    
    # Basic statistics
    total_properties = len(properties)
    locations = {}
    property_types = {}
    bedroom_counts = {}
    price_terms = {}
    
    for prop in properties:
        # Count locations
        location = prop.get('location', 'Unknown')
        locations[location] = locations.get(location, 0) + 1
        
        # Count property types
        prop_type = prop.get('property_type', 'Unknown')
        property_types[prop_type] = property_types.get(prop_type, 0) + 1
        
        # Count bedrooms
        bedrooms = prop.get('bedrooms', 0)
        bedroom_counts[bedrooms] = bedroom_counts.get(bedrooms, 0) + 1
        
        # Count payment terms
        payment_term = prop.get('payment_term', 'Unknown')
        price_terms[payment_term] = price_terms.get(payment_term, 0) + 1
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"BALI PROPERTY SCRAPING SUMMARY")
    print(f"{'='*60}")
    print(f"Total Properties Scraped: {total_properties}")
    
    print(f"\nTop Locations:")
    for location, count in sorted(locations.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {location}: {count} properties")
    
    print(f"\nProperty Types:")
    for prop_type, count in sorted(property_types.items(), key=lambda x: x[1], reverse=True):
        print(f"  {prop_type}: {count} properties")
    
    print(f"\nBedroom Distribution:")
    for bedrooms, count in sorted(bedroom_counts.items()):
        print(f"  {bedrooms} bedrooms: {count} properties")
    
    print(f"\nPayment Terms:")
    for term, count in sorted(price_terms.items(), key=lambda x: x[1], reverse=True):
        print(f"  {term}: {count} properties")
    
    print(f"{'='*60}\n")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "batches":
            print_batch_urls()
        elif sys.argv[1] == "combine":
            properties = combine_all_batches()
            generate_summary_report(properties)
        elif sys.argv[1] == "summary":
            try:
                with open('all_bali_properties.json', 'r', encoding='utf-8') as f:
                    properties = json.load(f)
                generate_summary_report(properties)
            except FileNotFoundError:
                print("No combined properties file found. Run 'python batch_scraper.py combine' first.")
    else:
        print("Usage:")
        print("  python batch_scraper.py batches  - Show batch URLs for manual processing")
        print("  python batch_scraper.py combine  - Combine all batch files")
        print("  python batch_scraper.py summary  - Show summary of combined data")
