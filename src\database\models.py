#!/usr/bin/env python3
"""
Database Models
Based on PRD: Database Layer
"""

from sqlalchemy import create_engine, Column, Integer, String, Text, Boolean, DateTime, JSON, Float, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime
from typing import Dict, Any, List, Optional

Base = declarative_base()

class Source(Base):
    """
    Table for tracking different property sources (websites)
    """
    __tablename__ = 'sources'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), unique=True, nullable=False)  # e.g., 'bali_home_immo'
    base_url = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    last_crawl_success = Column(DateTime)
    last_crawl_attempt = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationship
    listings = relationship("Listing", back_populates="source")
    crawl_sessions = relationship("CrawlSession", back_populates="source")

class Listing(Base):
    """
    Main listings table with gestructureerde opslag
    Based on PRD requirements
    """
    __tablename__ = 'listings'
    
    # Primary identification
    id = Column(Integer, primary_key=True)
    external_id = Column(String(100), nullable=False)  # property_id from source
    source_id = Column(Integer, ForeignKey('sources.id'), nullable=False)
    source_url = Column(String(500), nullable=False, unique=True)
    
    # Basic property info
    title = Column(String(500), nullable=False)
    price = Column(String(100))  # Keep as string to handle "Price On Request"
    price_numeric = Column(Float)  # Parsed numeric price for filtering
    payment_term = Column(String(20))  # monthly, yearly, both
    location = Column(String(200), nullable=False)
    bedrooms = Column(Integer)
    bathrooms = Column(Integer)
    ensuite_bathrooms = Column(Integer)
    
    # Property details
    property_type = Column(String(50))  # Villa, Townhouse, etc.
    status = Column(String(50), nullable=False)  # Available, Not Available, etc.
    availability_date = Column(String(50))
    furnishing = Column(String(50))
    view = Column(String(100))
    style_design = Column(String(100))
    surrounding = Column(String(100))
    floor_level = Column(Integer)
    
    # Technical specs
    electricity_capacity = Column(String(50))
    water_source = Column(String(50))
    parking = Column(String(50))
    internet = Column(String(100))
    air_conditioner_count = Column(Integer)
    year_built = Column(Integer)
    
    # Size information
    land_size_sqm = Column(Integer)
    building_size_sqm = Column(Integer)
    
    # Policies
    pet_policy = Column(String(200))
    sublease_allowed = Column(Boolean)
    
    # Text fields
    description = Column(Text)
    
    # JSON fields for complex data
    amenities = Column(JSON)  # Array of amenities
    indoor_details = Column(JSON)  # Living room, dining room, kitchen details
    outdoor_details = Column(JSON)  # Pool, garden, terrace details
    monthly_costs = Column(JSON)  # Cost breakdown
    images = Column(JSON)  # Array of image URLs
    additional_details = Column(JSON)  # Any other details
    
    # Media
    brochure_url = Column(String(500))
    
    # Metadata
    is_active = Column(Boolean, default=True)  # For marking as "verwijderd"
    scraped_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    source = relationship("Source", back_populates="listings")
    
    def __repr__(self):
        return f"<Listing(id={self.id}, external_id='{self.external_id}', title='{self.title[:50]}...')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'external_id': self.external_id,
            'title': self.title,
            'price': self.price,
            'price_numeric': self.price_numeric,
            'payment_term': self.payment_term,
            'location': self.location,
            'bedrooms': self.bedrooms,
            'bathrooms': self.bathrooms,
            'ensuite_bathrooms': self.ensuite_bathrooms,
            'property_type': self.property_type,
            'status': self.status,
            'availability_date': self.availability_date,
            'furnishing': self.furnishing,
            'view': self.view,
            'style_design': self.style_design,
            'surrounding': self.surrounding,
            'floor_level': self.floor_level,
            'electricity_capacity': self.electricity_capacity,
            'water_source': self.water_source,
            'parking': self.parking,
            'internet': self.internet,
            'air_conditioner_count': self.air_conditioner_count,
            'year_built': self.year_built,
            'land_size_sqm': self.land_size_sqm,
            'building_size_sqm': self.building_size_sqm,
            'pet_policy': self.pet_policy,
            'sublease_allowed': self.sublease_allowed,
            'description': self.description,
            'amenities': self.amenities,
            'indoor_details': self.indoor_details,
            'outdoor_details': self.outdoor_details,
            'monthly_costs': self.monthly_costs,
            'images': self.images,
            'additional_details': self.additional_details,
            'brochure_url': self.brochure_url,
            'source_url': self.source_url,
            'scraped_at': self.scraped_at.isoformat() if self.scraped_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class CrawlSession(Base):
    """
    Table for tracking crawl sessions and history
    Based on PRD: crawl_sessions table
    """
    __tablename__ = 'crawl_sessions'
    
    id = Column(Integer, primary_key=True)
    source_id = Column(Integer, ForeignKey('sources.id'), nullable=False)
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    status = Column(String(20), default='running')  # running, completed, failed
    
    # Statistics
    total_urls_processed = Column(Integer, default=0)
    successful_extractions = Column(Integer, default=0)
    failed_extractions = Column(Integer, default=0)
    available_properties_found = Column(Integer, default=0)
    
    # Error tracking
    error_message = Column(Text)
    error_details = Column(JSON)
    
    # Relationship
    source = relationship("Source", back_populates="crawl_sessions")
    
    def __repr__(self):
        return f"<CrawlSession(id={self.id}, source_id={self.source_id}, status='{self.status}')>"

# Database connection and session management
class DatabaseManager:
    """
    Database manager for handling connections and sessions
    """
    
    def __init__(self, database_url: str = "sqlite:///data/properties.db"):
        self.database_url = database_url
        self.engine = create_engine(database_url, echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
    def create_tables(self):
        """Create all tables"""
        Base.metadata.create_all(bind=self.engine)
        
    def get_session(self):
        """Get database session"""
        return self.SessionLocal()
    
    def close_session(self, session):
        """Close database session"""
        session.close()

# Utility functions for database operations
def parse_price_to_numeric(price_str: str) -> Optional[float]:
    """
    Parse price string to numeric value for filtering
    """
    if not price_str or price_str == "Price On Request":
        return None
    
    # Remove common formatting
    cleaned = price_str.replace('IDR', '').replace(',', '').replace('.', '').strip()
    
    try:
        return float(cleaned)
    except ValueError:
        return None

def create_listing_from_extracted_data(data: Dict[str, Any], source_id: int) -> Listing:
    """
    Create Listing object from extracted property data
    """
    # Parse price
    price_numeric = parse_price_to_numeric(data.get('price', ''))
    
    # Extract size information
    size_info = data.get('additional_details', {}).get('size', '')
    land_size = None
    building_size = None
    
    if isinstance(data.get('additional_details', {}).get('size'), dict):
        size_dict = data.get('additional_details', {}).get('size', {})
        land_size = size_dict.get('land_size_sqm')
        building_size = size_dict.get('building_size_sqm')
    
    listing = Listing(
        external_id=data.get('id', ''),
        source_id=source_id,
        source_url=data.get('source_url', ''),
        title=data.get('title', ''),
        price=data.get('price', ''),
        price_numeric=price_numeric,
        payment_term=data.get('payment_term', ''),
        location=data.get('location', ''),
        bedrooms=data.get('bedrooms'),
        bathrooms=data.get('bathrooms'),
        ensuite_bathrooms=data.get('ensuite_bathrooms'),
        property_type=data.get('additional_details', {}).get('property_type'),
        status=data.get('status', ''),
        availability_date=data.get('availability_date'),
        furnishing=data.get('additional_details', {}).get('furnishing'),
        view=data.get('additional_details', {}).get('view'),
        style_design=data.get('additional_details', {}).get('style_design'),
        surrounding=data.get('additional_details', {}).get('surrounding'),
        floor_level=data.get('additional_details', {}).get('floor_level'),
        electricity_capacity=data.get('additional_details', {}).get('electricity_capacity'),
        water_source=data.get('additional_details', {}).get('water_source'),
        parking=data.get('additional_details', {}).get('parking'),
        internet=data.get('additional_details', {}).get('internet'),
        air_conditioner_count=data.get('additional_details', {}).get('air_conditioner_count'),
        year_built=data.get('additional_details', {}).get('year_built'),
        land_size_sqm=land_size,
        building_size_sqm=building_size,
        pet_policy=data.get('additional_details', {}).get('pet_policy'),
        sublease_allowed=data.get('additional_details', {}).get('sublease_allowed'),
        description=data.get('description', ''),
        amenities=data.get('amenities', []),
        indoor_details=data.get('additional_details', {}).get('indoor_details', {}),
        outdoor_details=data.get('additional_details', {}).get('outdoor_details', {}),
        monthly_costs=data.get('additional_details', {}).get('monthly_costs', {}),
        images=data.get('images', []),
        additional_details=data.get('additional_details', {}),
        brochure_url=data.get('brochure_url'),
        scraped_at=datetime.fromisoformat(data.get('scraped_at')) if data.get('scraped_at') else datetime.utcnow()
    )
    
    return listing
