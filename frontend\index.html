<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏡 Bali Property Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.assistant .message-content {
            background: white;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .property-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 16px;
            margin: 10px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .property-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .property-details {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .property-highlights {
            margin-top: 8px;
        }

        .highlight-tag {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 6px;
            margin-top: 4px;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .chat-input-wrapper {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #4facfe;
        }

        .send-button, .voice-button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s;
        }

        .voice-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .voice-button.recording {
            background: linear-gradient(135deg, #ff3838 0%, #c0392b 100%);
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .send-button:hover, .voice-button:hover {
            transform: scale(1.05);
        }

        .send-button:disabled, .voice-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .quick-replies {
            display: flex;
            gap: 8px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .quick-reply {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .quick-reply:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            color: #666;
            font-style: italic;
            margin: 10px 0;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #666;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .status-indicator {
            text-align: center;
            padding: 10px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 14px;
            color: #856404;
        }

        .status-indicator.connected {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .status-indicator.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🏡 Bali Property Assistant</h1>
            <p>Find your perfect home in Bali with AI assistance</p>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div id="statusIndicator" class="status-indicator">
                Connecting to property database...
            </div>
        </div>
        
        <div class="typing-indicator" id="typingIndicator">
            <span>Assistant is typing</span>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
        
        <div class="chat-input-container">
            <div class="chat-input-wrapper">
                <input 
                    type="text" 
                    id="chatInput" 
                    class="chat-input" 
                    placeholder="Tell me what kind of property you're looking for..."
                    disabled
                >
                <button id="voiceButton" class="voice-button" disabled>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/>
                    </svg>
                </button>
                <button id="sendButton" class="send-button" disabled>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                </button>
            </div>
            <div class="quick-replies" id="quickReplies" style="display: none;">
                <div class="quick-reply" onclick="sendQuickReply('Show me villas in Canggu')">Villas in Canggu</div>
                <div class="quick-reply" onclick="sendQuickReply('2 bedroom apartment under 30 million')">2BR under 30M</div>
                <div class="quick-reply" onclick="sendQuickReply('Pet-friendly properties')">Pet-friendly</div>
                <div class="quick-reply" onclick="sendQuickReply('Properties with pool')">With pool</div>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE_URL = 'http://localhost:8000';
        
        // DOM elements
        const chatMessages = document.getElementById('chatMessages');
        const chatInput = document.getElementById('chatInput');
        const sendButton = document.getElementById('sendButton');
        const voiceButton = document.getElementById('voiceButton');
        const typingIndicator = document.getElementById('typingIndicator');
        const statusIndicator = document.getElementById('statusIndicator');
        const quickReplies = document.getElementById('quickReplies');
        
        // State
        let isConnected = false;
        let conversationHistory = [];
        let currentSessionId = null;
        let voiceRecorder = null;
        let isRecording = false;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkAPIConnection();
            setupEventListeners();
            showWelcomeMessage();
        });
        
        function setupEventListeners() {
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
            
            sendButton.addEventListener('click', sendMessage);
            voiceButton.addEventListener('click', toggleVoiceRecording);
        }
        
        async function checkAPIConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/`);
                if (response.ok) {
                    isConnected = true;
                    statusIndicator.textContent = '✅ Connected to property database';
                    statusIndicator.className = 'status-indicator connected';
                    enableInput();
                } else {
                    throw new Error('API not responding');
                }
            } catch (error) {
                isConnected = false;
                statusIndicator.textContent = '⚠️ Running in demo mode - API not available';
                statusIndicator.className = 'status-indicator error';
                enableInput(); // Still allow demo mode
            }
        }
        
        function enableInput() {
            chatInput.disabled = false;
            sendButton.disabled = false;
            voiceButton.disabled = false;
            chatInput.focus();
            quickReplies.style.display = 'flex';
        }
        
        function showWelcomeMessage() {
            const welcomeMessage = `Welcome to Bali Property Assistant! 🏡

I'm here to help you find the perfect property in Bali. I can search through:
• Budget rentals (IDR 15M-150M/month)
• Premium furnished rentals (USD 1.5K-10K/month)  
• Investment properties (USD 120K-2M+)

Popular locations include Canggu, Seminyak, Ubud, Kedungu, and Jimbaran.

What kind of property are you looking for? 🌴`;
            
            addMessage('assistant', welcomeMessage);
        }
        
        function addMessage(role, content, isHTML = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            if (isHTML) {
                contentDiv.innerHTML = content;
            } else {
                contentDiv.textContent = content;
            }
            
            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);
            
            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            // Add to history
            conversationHistory.push({ role, content, timestamp: Date.now() });
        }
        
        function showTyping() {
            typingIndicator.style.display = 'flex';
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        function hideTyping() {
            typingIndicator.style.display = 'none';
        }
        
        async function sendMessage() {
            const message = chatInput.value.trim();
            if (!message) return;
            
            // Add user message
            addMessage('user', message);
            chatInput.value = '';
            
            // Show typing indicator
            showTyping();
            
            try {
                // Simulate processing delay
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                let response;
                if (isConnected) {
                    response = await getAIResponse(message);
                } else {
                    response = getDemoResponse(message);
                }
                
                hideTyping();
                
                // Check if response contains property listings
                if (response.includes('properties matching')) {
                    addMessage('assistant', response, true);
                } else {
                    addMessage('assistant', response);
                }
                
            } catch (error) {
                hideTyping();
                addMessage('assistant', 'Sorry, I encountered an error. Please try again.');
                console.error('Error:', error);
            }
        }
        
        async function getAIResponse(message) {
            try {
                // Use enhanced chat endpoint with OpenAI integration
                const response = await fetch(`${API_BASE_URL}/api/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: currentSessionId
                    })
                });

                if (response.ok) {
                    const result = await response.json();

                    if (result.success) {
                        // Update session ID
                        currentSessionId = result.session_id;

                        // Handle handoff
                        if (result.handoff_triggered) {
                            setTimeout(() => {
                                addMessage('system', '📞 Your request has been forwarded to our sales team. They will contact you soon!');
                            }, 1000);
                        }

                        return result.response;
                    } else {
                        return "I'm sorry, I encountered an error. Please try again.";
                    }
                } else {
                    // Fallback to simple search
                    return await getFallbackResponse(message);
                }

            } catch (error) {
                console.error('AI response error:', error);
                return await getFallbackResponse(message);
            }
        }

        async function getFallbackResponse(message) {
            // Fallback to simple search if OpenAI is not available
            const searchParams = extractSearchParams(message);

            if (Object.keys(searchParams).length > 0) {
                try {
                    const response = await fetch(`${API_BASE_URL}/api/search`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({...searchParams, limit: 3})
                    });

                    if (response.ok) {
                        const results = await response.json();
                        return formatSearchResults(results);
                    }
                } catch (error) {
                    console.error('Search fallback error:', error);
                }
            }

            // Generate clarifying response
            return generateClarifyingResponse(message, searchParams);
        }
        
        function extractSearchParams(message) {
            const params = {};
            const messageLower = message.toLowerCase();
            
            // Location extraction
            const locations = ['canggu', 'seminyak', 'ubud', 'kedungu', 'jimbaran'];
            for (const location of locations) {
                if (messageLower.includes(location)) {
                    params.location = location.charAt(0).toUpperCase() + location.slice(1);
                    break;
                }
            }
            
            // Bedroom extraction
            const bedroomMatch = messageLower.match(/(\d+)\s*bedroom/);
            if (bedroomMatch) {
                params.bedrooms = parseInt(bedroomMatch[1]);
            }
            
            // Property type
            if (messageLower.includes('villa')) params.property_type = 'Villa';
            if (messageLower.includes('apartment')) params.property_type = 'Apartment';
            
            // Price extraction
            const priceMatch = messageLower.match(/(\d+)\s*million/);
            if (priceMatch) {
                params.max_price = parseInt(priceMatch[1]) * 1000000;
            }
            
            // Amenities
            if (messageLower.includes('pool')) params.pool = true;
            if (messageLower.includes('pet')) params.pet_friendly = true;
            
            return params;
        }
        
        function formatSearchResults(results) {
            if (results.total === 0) {
                return "I couldn't find any properties matching your criteria. Would you like to adjust your search?";
            }
            
            let html = `I found ${results.total} properties matching your criteria. Here are the top results:<br><br>`;
            
            results.properties.forEach((property, index) => {
                const highlights = property.highlights.map(h => 
                    `<span class="highlight-tag">${h}</span>`
                ).join('');
                
                html += `
                    <div class="property-card">
                        <div class="property-title">${property.title}</div>
                        <div class="property-details">
                            📍 ${property.location}<br>
                            🛏️ ${property.bedrooms} bedrooms, ${property.bathrooms} bathrooms<br>
                            💰 ${formatPrice(property.price)}<br>
                            🏠 ${property.property_type}<br>
                            🔗 Source: ${property.source.replace('_', ' ')}
                        </div>
                        <div class="property-highlights">${highlights}</div>
                    </div>
                `;
            });
            
            if (results.total > 3) {
                html += `<br>There are ${results.total - 3} more properties available. Would you like to see more or refine your search?`;
            }
            
            return html;
        }
        
        function formatPrice(priceInfo) {
            const display = priceInfo.display;
            const currency = priceInfo.currency;
            const type = priceInfo.type;
            
            let formatted = `${currency} ${display.toLocaleString()}`;
            if (type === 'rental') formatted += ' per month';
            if (type === 'purchase') formatted += ' (purchase)';
            
            return formatted;
        }
        
        function generateClarifyingResponse(message, params) {
            if (!params.location) {
                return `I'd love to help you find a property! Which area of Bali interests you?

Popular areas include:
🏄‍♂️ Canggu - Great for surfers and digital nomads
🏖️ Seminyak - Beach clubs and nightlife  
🌿 Ubud - Rice fields and wellness retreats
🏔️ Kedungu - Emerging area with great views
🐠 Jimbaran - Family-friendly with seafood

Where would you like to be located?`;
            }
            
            if (!params.bedrooms) {
                return `Great choice on ${params.location}! How many bedrooms do you need?`;
            }
            
            return "Could you tell me more about your budget range or any specific requirements?";
        }
        
        function getDemoResponse(message) {
            return `Thanks for your message: "${message}"

This is demo mode since the API is not available. In the full version, I would:
1. Analyze your requirements
2. Search through 1600+ properties from 3 sources
3. Show you matching results with images and details
4. Help you contact property owners

Try asking about properties in Canggu, Seminyak, or Ubud!`;
        }
        
        function sendQuickReply(message) {
            chatInput.value = message;
            sendMessage();
        }

        // Voice Recording Functions
        async function toggleVoiceRecording() {
            if (isRecording) {
                stopVoiceRecording();
            } else {
                await startVoiceRecording();
            }
        }

        async function startVoiceRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

                voiceRecorder = new MediaRecorder(stream);
                const audioChunks = [];

                voiceRecorder.ondataavailable = (event) => {
                    audioChunks.push(event.data);
                };

                voiceRecorder.onstop = async () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    await sendVoiceMessage(audioBlob);

                    // Stop all tracks
                    stream.getTracks().forEach(track => track.stop());
                };

                voiceRecorder.start();
                isRecording = true;

                // Update UI
                voiceButton.classList.add('recording');
                voiceButton.title = 'Click to stop recording';

                // Show recording indicator
                addMessage('system', '🎤 Recording... Click the microphone to stop.');

            } catch (error) {
                console.error('Error starting recording:', error);
                alert('Could not access microphone. Please check permissions.');
            }
        }

        function stopVoiceRecording() {
            if (voiceRecorder && isRecording) {
                voiceRecorder.stop();
                isRecording = false;

                // Update UI
                voiceButton.classList.remove('recording');
                voiceButton.title = 'Click to record voice message';
            }
        }

        async function sendVoiceMessage(audioBlob) {
            try {
                showTyping();

                const formData = new FormData();
                formData.append('audio_file', audioBlob, 'recording.wav');
                if (currentSessionId) {
                    formData.append('session_id', currentSessionId);
                }

                const response = await fetch(`${API_BASE_URL}/api/voice-chat`, {
                    method: 'POST',
                    body: formData
                });

                hideTyping();

                if (response.ok) {
                    const result = await response.json();

                    if (result.success) {
                        // Update session ID
                        currentSessionId = result.session_id;

                        // Show transcription
                        if (result.transcription) {
                            addMessage('user', `🎤 "${result.transcription}"`);
                        }

                        // Show response
                        if (result.response) {
                            if (result.response.includes('properties matching')) {
                                addMessage('assistant', result.response, true);
                            } else {
                                addMessage('assistant', result.response);
                            }
                        }

                        // Handle handoff
                        if (result.handoff_triggered) {
                            addMessage('system', '📞 Your request has been forwarded to our sales team. They will contact you soon!');
                        }

                    } else {
                        addMessage('assistant', 'Sorry, I couldn\'t understand your voice message. Please try again or type your message.');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }

            } catch (error) {
                hideTyping();
                console.error('Error sending voice message:', error);
                addMessage('assistant', 'Sorry, there was an error processing your voice message. Please try again.');
            }
        }
    </script>
</body>
</html>
