{"type": "object", "properties": {"title": {"type": "string"}, "price": {"type": "string"}, "payment_term": {"type": "string"}, "location": {"type": "string"}, "bedrooms": {"type": "integer"}, "bathrooms": {"type": "integer"}, "ensuite_bathrooms": {"type": "integer"}, "description": {"type": "string"}, "amenities": {"type": "array", "items": {"type": "string"}}, "size": {"type": "object", "properties": {"land_size_sqm": {"type": "integer"}, "building_size_sqm": {"type": "integer"}}}, "year_built": {"type": "integer"}, "property_type": {"type": "string"}, "property_id": {"type": "string"}, "status": {"type": "string"}, "availability_date": {"type": "string"}, "furnishing": {"type": "string"}, "view": {"type": "string"}, "style_design": {"type": "string"}, "surrounding": {"type": "string"}, "floor_level": {"type": "integer"}, "electricity_capacity": {"type": "string"}, "water_source": {"type": "string"}, "parking": {"type": "string"}, "internet": {"type": "string"}, "air_conditioner_count": {"type": "integer"}, "pet_policy": {"type": "string"}, "sublease_allowed": {"type": "boolean"}, "indoor_details": {"type": "object", "properties": {"living_room": {"type": "string"}, "dining_room": {"type": "string"}, "kitchen": {"type": "string"}}}, "outdoor_details": {"type": "object", "properties": {"swimming_pool": {"type": "boolean"}, "pool_size": {"type": "string"}, "garden": {"type": "boolean"}, "terrace": {"type": "boolean"}}}, "monthly_costs": {"type": "object", "properties": {"banjar_fee_security": {"type": "string"}, "cleaning_service": {"type": "string"}, "pool_maintenance": {"type": "string"}, "garden_maintenance": {"type": "string"}, "bin_collection": {"type": "string"}, "electricity": {"type": "string"}, "internet_included": {"type": "string"}}}, "yearly_costs": {"type": "object", "properties": {"banjar_fee_security": {"type": "string"}, "cleaning_service": {"type": "string"}, "pool_maintenance": {"type": "string"}, "garden_maintenance": {"type": "string"}, "bin_collection": {"type": "string"}, "electricity": {"type": "string"}, "internet_included": {"type": "string"}}}, "images": {"type": "array", "items": {"type": "string"}}, "brochure_url": {"type": "string"}, "detail_url": {"type": "string"}}, "required": ["title", "price", "location", "bedrooms", "bathrooms", "property_id", "detail_url"]}