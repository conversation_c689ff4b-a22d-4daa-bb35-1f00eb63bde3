#!/usr/bin/env python3
"""
Test script for the API server
"""

import requests
import json
import time

API_BASE_URL = "http://localhost:8000"

def test_api_health():
    """Test API health check"""
    print("🔍 Testing API Health...")
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API is running!")
            print(f"   Status: {data['status']}")
            print(f"   Total Properties: {data['total_properties']}")
            print(f"   Sources: {', '.join(data['sources'])}")
            return True
        else:
            print(f"❌ API returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API connection failed: {str(e)}")
        return False

def test_property_search():
    """Test property search endpoint"""
    print("\n🔍 Testing Property Search...")
    try:
        # Test basic search
        search_data = {
            "location": "Canggu",
            "bedrooms": 2
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/search",
            json=search_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Search successful!")
            print(f"   Total Results: {data['total']}")
            print(f"   Properties Returned: {len(data['properties'])}")
            print(f"   Sources: {data['sources']}")
            
            if data['properties']:
                prop = data['properties'][0]
                print(f"   Sample Property: {prop['title']}")
                print(f"   Location: {prop['location']}")
                print(f"   Price: {prop['price']['currency']} {prop['price']['display']:,}")
            
            return True
        else:
            print(f"❌ Search failed with status: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Search test failed: {str(e)}")
        return False

def test_chat_endpoint():
    """Test chat endpoint"""
    print("\n🔍 Testing Chat Endpoint...")
    try:
        chat_data = {
            "message": "Show me 2 bedroom villas in Canggu with pool"
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/chat",
            json=chat_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Chat successful!")
            print(f"   Success: {data['success']}")
            print(f"   Session ID: {data['session_id']}")
            print(f"   Response Preview: {data['response'][:100]}...")
            return True
        else:
            print(f"❌ Chat failed with status: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chat test failed: {str(e)}")
        return False

def test_locations_endpoint():
    """Test locations endpoint"""
    print("\n🔍 Testing Locations Endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/api/locations", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Locations retrieved!")
            print(f"   Total Locations: {data['total']}")
            print(f"   Available: {', '.join(data['locations'])}")
            return True
        else:
            print(f"❌ Locations failed with status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Locations test failed: {str(e)}")
        return False

def test_stats_endpoint():
    """Test stats endpoint"""
    print("\n🔍 Testing Stats Endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/api/stats", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Stats retrieved!")
            print(f"   Total Listings: {data['total_listings']}")
            print(f"   Available Listings: {data['available_listings']}")
            print(f"   Sources: {data['sources']}")
            return True
        else:
            print(f"❌ Stats failed with status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Stats test failed: {str(e)}")
        return False

def run_all_tests():
    """Run all API tests"""
    print("🧪 TESTING BALI PROPERTY SEARCH API")
    print("=" * 50)
    
    # Wait a moment for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    tests = [
        ("API Health", test_api_health),
        ("Property Search", test_property_search),
        ("Chat Endpoint", test_chat_endpoint),
        ("Locations Endpoint", test_locations_endpoint),
        ("Stats Endpoint", test_stats_endpoint)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! API is working correctly.")
        print("\n🌐 You can now test the web interface:")
        print("   1. Open frontend/index.html in your browser")
        print("   2. Try chatting: 'Show me villas in Canggu'")
        print("   3. API docs: http://localhost:8000/docs")
    else:
        print("⚠️ Some tests failed. Check the API server logs.")
    
    return passed == len(tests)

if __name__ == "__main__":
    run_all_tests()
