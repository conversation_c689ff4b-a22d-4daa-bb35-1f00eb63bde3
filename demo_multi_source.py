#!/usr/bin/env python3
"""
Multi-Source Demo
Shows the new system with both Bali Home Immo and BetterPlace
"""

import json
import sys
import os
from pathlib import Path

# Add src to path
sys.path.append('src')

def demo_bali_home_immo():
    """Demo Bali Home Immo property"""
    print("🏡 BALI HOME IMMO - RENTAL PROPERTIES")
    print("=" * 50)
    
    sample_property = {
        "title": "COZY 2 BEDROOMS VILLA - AD019",
        "price": "19.000.000",
        "payment_term": "monthly",
        "location": "Pandawa, Kutuh",
        "bedrooms": 2,
        "bathrooms": 2,
        "status": "Available",
        "property_type": "Villa",
        "property_id": "AD019",
        "pet_policy": "Pet-friendly",
        "sublease_allowed": True,
        "monthly_costs": {
            "cleaning_service": "Included",
            "pool_maintenance": "Included",
            "electricity": "Not included"
        },
        "images": [
            "https://bali-home-immo.com/images/properties/cozy-2-bedrooms-villa-for-monthly-rental-in-bali-pandawa-kutuh-ad019EUPzHdMt6YFOSOjuV0xP1745802475.png",
            "https://bali-home-immo.com/images/properties/cozy-2-bedrooms-villa-for-monthly-rental-in-bali-pandawa-kutuh-ad0192H86KF19gmYbyxg0lddv1745802540.png"
        ],
        "source": "bali_home_immo"
    }
    
    print(f"✅ Property: {sample_property['title']}")
    print(f"💰 Price: IDR {sample_property['price']} ({sample_property['payment_term']})")
    print(f"📍 Location: {sample_property['location']}")
    print(f"🏠 Type: {sample_property['property_type']} | ID: {sample_property['property_id']}")
    print(f"🛏️ Bedrooms: {sample_property['bedrooms']} | Bathrooms: {sample_property['bathrooms']}")
    print(f"🐕 Pet Policy: {sample_property['pet_policy']}")
    print(f"📸 Images: {len(sample_property['images'])} URLs")
    print(f"💡 Focus: Monthly rentals, pet policies, cost breakdowns")
    
    return sample_property

def demo_betterplace():
    """Demo BetterPlace property"""
    print("\n🏢 BETTERPLACE - INVESTMENT PROPERTIES")
    print("=" * 50)
    
    sample_property = {
        "title": "Modern 3 Bedroom Villa with Rooftop in Pangkung Tibah, Near Kedungu",
        "price": "IDR 4,866,049,227",
        "price_usd": "299,000",
        "ownership_type": "Leasehold",
        "location": "Kedungu",
        "bedrooms": 3,
        "bathrooms": 4,
        "property_type": "Villa",
        "property_id": "BPVL02232",
        "year_built": 2024,
        "lease_duration": "26 Years",
        "lease_expiry": "Q3 2051",
        "construction_status": "Completed",
        "land_size_sqm": 260,
        "building_size_sqm": 166,
        "price_per_sqm": "29,313,550",
        "distance_to_beach": "3 Minutes",
        "pool_type": "Private",
        "listing_agent": "Lia",
        "agent_contact": "+62 811-3900-2007",
        "images": [
            "https://betterplace.cc/_next/image?url=https%3A%2F%2Fbetterplace.sgp1.cdn.digitaloceanspaces.com%2FCACHE%2Fimages%2Fprocessed%2F4571cdc9-e0fe-4d5f-ae04-16cf97aff72c_f4bead9d561036c48c9f7be48765ba7ee36ee95ea5aa0c046%2F575a0e09b65ae1d24d3831876477938d.webp&w=3840&q=75",
            "https://betterplace.cc/_next/image?url=https%3A%2F%2Fbetterplace.sgp1.cdn.digitaloceanspaces.com%2FCACHE%2Fimages%2Fprocessed%2F953b319f-f115-4e4a-ad25-fab8118f1599_a502e8d3ad8ad550b3ad641b7f0bf64d1f0313301ecaab1e7%2F9cb777c7b7ff187504f633d1a95dfdea.webp&w=3840&q=75"
        ],
        "source": "betterplace"
    }
    
    print(f"✅ Property: {sample_property['title'][:50]}...")
    print(f"💰 Price: {sample_property['price']} (USD {sample_property['price_usd']})")
    print(f"🏛️ Ownership: {sample_property['ownership_type']} ({sample_property['lease_duration']})")
    print(f"📍 Location: {sample_property['location']} ({sample_property['distance_to_beach']} to beach)")
    print(f"🏠 Type: {sample_property['property_type']} | ID: {sample_property['property_id']}")
    print(f"🛏️ Bedrooms: {sample_property['bedrooms']} | Bathrooms: {sample_property['bathrooms']}")
    print(f"📐 Size: {sample_property['land_size_sqm']}sqm land, {sample_property['building_size_sqm']}sqm building")
    print(f"💵 Price/sqm: IDR {sample_property['price_per_sqm']}")
    print(f"👤 Agent: {sample_property['listing_agent']} ({sample_property['agent_contact']})")
    print(f"📸 Images: {len(sample_property['images'])} URLs")
    print(f"💡 Focus: Investment properties, leasehold/freehold, ROI data")
    
    return sample_property

def demo_comparison():
    """Compare the two sources"""
    print("\n🔄 SOURCE COMPARISON")
    print("=" * 50)
    
    comparison = {
        "bali_home_immo": {
            "focus": "Monthly/Yearly Rentals",
            "price_range": "IDR 15M - 150M/month",
            "unique_features": [
                "Pet policies",
                "Sublease information", 
                "Monthly cost breakdowns",
                "Availability filtering",
                "Payment terms"
            ],
            "property_count": "49 URLs discovered",
            "target_market": "Renters, Digital Nomads"
        },
        "betterplace": {
            "focus": "Investment Properties",
            "price_range": "USD 120K - 2M+",
            "unique_features": [
                "Leasehold/Freehold distinction",
                "ROI calculations",
                "Investment potential",
                "Lease expiry dates",
                "Price per sqm"
            ],
            "property_count": "1378+ listings available",
            "target_market": "Investors, Property Buyers"
        }
    }
    
    for source, details in comparison.items():
        print(f"\n📊 {source.upper().replace('_', ' ')}")
        print(f"   Focus: {details['focus']}")
        print(f"   Price Range: {details['price_range']}")
        print(f"   Target Market: {details['target_market']}")
        print(f"   Property Count: {details['property_count']}")
        print(f"   Unique Features:")
        for feature in details['unique_features']:
            print(f"     • {feature}")

def demo_unified_output():
    """Show how both sources feed into unified system"""
    print("\n🔗 UNIFIED DATA PIPELINE")
    print("=" * 50)
    
    unified_format = {
        "common_fields": [
            "title", "price", "location", "bedrooms", "bathrooms",
            "property_type", "property_id", "images", "detail_url"
        ],
        "source_specific_fields": {
            "bali_home_immo": [
                "payment_term", "monthly_costs", "pet_policy", 
                "sublease_allowed", "availability_date"
            ],
            "betterplace": [
                "ownership_type", "lease_duration", "price_per_sqm",
                "investment_potential", "listing_agent", "roi_info"
            ]
        },
        "database_storage": {
            "listings_table": "Common fields + JSON for source-specific data",
            "sources_table": "Source metadata and configuration",
            "crawl_sessions": "Track ingestion history per source"
        }
    }
    
    print("✅ COMMON FIELDS (All Sources):")
    for field in unified_format["common_fields"]:
        print(f"   • {field}")
    
    print("\n🔧 SOURCE-SPECIFIC FIELDS:")
    for source, fields in unified_format["source_specific_fields"].items():
        print(f"   {source.upper().replace('_', ' ')}:")
        for field in fields:
            print(f"     • {field}")
    
    print("\n🗃️ DATABASE STORAGE:")
    for table, description in unified_format["database_storage"].items():
        print(f"   {table}: {description}")

def demo_next_steps():
    """Show next steps for multi-source system"""
    print("\n🚀 NEXT STEPS - MULTI-SOURCE EXPANSION")
    print("=" * 50)
    
    next_steps = [
        "🔍 URL Discovery: Implement automatic property URL discovery for BetterPlace",
        "🌐 More Sources: Add Villa-Bali.com, Bali-Real-Estate.com, etc.",
        "🔄 Unified API: Single endpoint returning properties from all sources",
        "📊 Cross-Source Analytics: Compare prices across different platforms",
        "🤖 Smart Matching: Match similar properties across sources",
        "⚡ Real-time Updates: Monitor all sources for new listings",
        "🎯 Source-Specific Filters: Filter by rental vs investment properties"
    ]
    
    for step in next_steps:
        print(step)

def save_multi_source_data(bali_property, betterplace_property):
    """Save demo data from both sources"""
    Path("data/raw").mkdir(parents=True, exist_ok=True)
    
    multi_source_data = {
        "sources": {
            "bali_home_immo": bali_property,
            "betterplace": betterplace_property
        },
        "comparison": {
            "bali_focus": "Rentals with detailed cost breakdowns",
            "betterplace_focus": "Investment properties with ROI data",
            "common_fields": ["title", "price", "location", "bedrooms", "bathrooms"],
            "unique_value": "Comprehensive market coverage"
        }
    }
    
    filename = "data/raw/multi_source_demo.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(multi_source_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Multi-source data saved to: {filename}")

def main():
    """Run the complete multi-source demo"""
    print("🌟 MULTI-SOURCE REAL ESTATE DATA PIPELINE")
    print("=" * 60)
    
    # Demo both sources
    bali_property = demo_bali_home_immo()
    betterplace_property = demo_betterplace()
    
    # Show comparison
    demo_comparison()
    
    # Show unified system
    demo_unified_output()
    
    # Show next steps
    demo_next_steps()
    
    # Save demo data
    save_multi_source_data(bali_property, betterplace_property)
    
    print("\n" + "=" * 60)
    print("🎉 MULTI-SOURCE DEMO COMPLETE!")
    print("=" * 60)
    print("The system now supports:")
    print("1. 🏠 Bali Home Immo (Rentals) - 49 properties")
    print("2. 🏢 BetterPlace (Investments) - 1378+ properties")
    print("3. 🔗 Unified data pipeline with source-specific fields")
    print("4. 📊 Cross-source comparison capabilities")
    print("\nTo test both sources:")
    print("  python demo_multi_source.py")
    print("  cd src/data_ingestion && python ingestion_service.py ingest --source bali_home_immo --limit 3")
    print("  cd src/data_ingestion && python ingestion_service.py ingest --source betterplace --limit 3")

if __name__ == "__main__":
    main()
