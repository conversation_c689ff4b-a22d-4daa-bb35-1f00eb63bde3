#!/usr/bin/env python3
"""
Conversational AI Assistant for Bali Property Search
Based on PRD: Conversational Layer & UX
"""

import json
import requests
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ConversationState:
    """Track conversation state and user preferences"""
    location: Optional[str] = None
    bedrooms: Optional[int] = None
    bathrooms: Optional[int] = None
    max_price: Optional[int] = None
    min_price: Optional[int] = None
    property_type: Optional[str] = None
    term: Optional[str] = None
    furnished: Optional[bool] = None
    pool: Optional[bool] = None
    pet_friendly: Optional[bool] = None
    conversation_phase: str = "exploration"  # exploration, summary, results, help
    last_search_results: List[Dict] = None
    
    def to_search_params(self) -> Dict[str, Any]:
        """Convert state to search API parameters"""
        params = {}
        if self.location:
            params['location'] = self.location
        if self.bedrooms:
            params['bedrooms'] = self.bedrooms
        if self.bathrooms:
            params['bathrooms'] = self.bathrooms
        if self.max_price:
            params['max_price'] = self.max_price
        if self.min_price:
            params['min_price'] = self.min_price
        if self.property_type:
            params['property_type'] = self.property_type
        if self.term:
            params['term'] = self.term
        if self.furnished is not None:
            params['furnished'] = self.furnished
        if self.pool is not None:
            params['pool'] = self.pool
        if self.pet_friendly is not None:
            params['pet_friendly'] = self.pet_friendly
        return params

class PropertyAssistant:
    """
    AI Assistant for property search conversations
    Implements the PRD conversational flow
    """
    
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        self.api_base_url = api_base_url
        self.system_prompt = self._get_system_prompt()
        self.conversation_state = ConversationState()
        
    def _get_system_prompt(self) -> str:
        """Get the system prompt from PRD"""
        return """You are a helpful, friendly property assistant specialized in long-term rentals and real estate in Bali. Your task is to help users find a home by asking conversational, human questions about what they're looking for, such as location, price range, property type, number of bedrooms, bathrooms, desired start date and lease duration.

Always use the `search_properties` function to perform a search once you have enough information. Never invent results. Show a maximum of 3 listings at a time, and offer users the option to refine or broaden their filters. If you're unsure about their request, ask clarifying questions. Keep your tone supportive and to the point.

Available locations in Bali include: Canggu, Seminyak, Ubud, Kedungu, Jimbaran, Pandawa, Padonan, Babakan, Uluwatu, and more.

Property types available: Villa, Apartment, House, Land.

Price ranges vary from IDR 15M-150M per month for rentals, and USD 120K-2M+ for purchases.

You have access to three property sources:
1. Bali Home Immo - Budget rentals with pet policies and cost breakdowns
2. BetterPlace - Investment properties with ROI data and leasehold information  
3. Bali Villa Realty - Premium furnished rentals with multi-currency pricing

Always ask clarifying questions if the user's request is unclear, and summarize their preferences before searching."""

    def search_properties(self, **kwargs) -> Dict[str, Any]:
        """
        Function calling implementation for property search
        This is the main function the AI assistant can call
        """
        try:
            # Update conversation state with new parameters
            for key, value in kwargs.items():
                if hasattr(self.conversation_state, key) and value is not None:
                    setattr(self.conversation_state, key, value)
            
            # Make API call to search endpoint
            search_params = self.conversation_state.to_search_params()
            search_params.update(kwargs)  # Override with any new parameters
            search_params['limit'] = 3  # As per PRD: max 3 results
            
            logger.info(f"Searching with parameters: {search_params}")
            
            response = requests.post(
                f"{self.api_base_url}/api/search",
                json=search_params,
                timeout=30
            )
            
            if response.status_code == 200:
                results = response.json()
                self.conversation_state.last_search_results = results.get('properties', [])
                self.conversation_state.conversation_phase = "results"
                return results
            else:
                logger.error(f"Search API error: {response.status_code} - {response.text}")
                return {
                    "error": f"Search failed with status {response.status_code}",
                    "total": 0,
                    "properties": []
                }
                
        except Exception as e:
            logger.error(f"Search error: {str(e)}")
            return {
                "error": f"Search error: {str(e)}",
                "total": 0,
                "properties": []
            }
    
    def get_function_schema(self) -> Dict[str, Any]:
        """
        Get the function calling schema for search_properties
        Based on PRD function calling requirements
        """
        return {
            "name": "search_properties",
            "description": "Search for properties in Bali based on user preferences",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "Location/area in Bali (e.g., Canggu, Seminyak, Ubud)"
                    },
                    "bedrooms": {
                        "type": "integer",
                        "description": "Number of bedrooms",
                        "minimum": 1,
                        "maximum": 10
                    },
                    "bathrooms": {
                        "type": "integer", 
                        "description": "Number of bathrooms",
                        "minimum": 1,
                        "maximum": 10
                    },
                    "max_price": {
                        "type": "integer",
                        "description": "Maximum price in IDR"
                    },
                    "min_price": {
                        "type": "integer",
                        "description": "Minimum price in IDR"
                    },
                    "property_type": {
                        "type": "string",
                        "description": "Type of property",
                        "enum": ["Villa", "Apartment", "House", "Land"]
                    },
                    "term": {
                        "type": "string",
                        "description": "Rental term or purchase",
                        "enum": ["monthly", "yearly", "purchase"]
                    },
                    "furnished": {
                        "type": "boolean",
                        "description": "Whether property should be furnished"
                    },
                    "pool": {
                        "type": "boolean",
                        "description": "Whether property should have a swimming pool"
                    },
                    "pet_friendly": {
                        "type": "boolean",
                        "description": "Whether property should be pet-friendly"
                    }
                },
                "required": []
            }
        }
    
    def format_search_results(self, results: Dict[str, Any]) -> str:
        """
        Format search results for display in conversation
        """
        if results.get('error'):
            return f"I'm sorry, there was an error with the search: {results['error']}"
        
        total = results.get('total', 0)
        properties = results.get('properties', [])
        
        if total == 0:
            return """I couldn't find any properties matching your criteria. Would you like me to:
1. Broaden the search by adjusting the price range or location?
2. Look for different property types?
3. Check properties with different amenities?

Let me know how you'd like to adjust the search!"""
        
        # Format property listings
        formatted_results = f"I found {total} properties matching your criteria. Here are the top 3:\n\n"
        
        for i, prop in enumerate(properties[:3], 1):
            price_display = self._format_price(prop.get('price', {}))
            highlights = ", ".join(prop.get('highlights', []))
            
            formatted_results += f"""**{i}. {prop.get('title', 'Property')}**
📍 Location: {prop.get('location', 'N/A')}
🛏️ {prop.get('bedrooms', 0)} bedrooms, {prop.get('bathrooms', 0)} bathrooms
💰 Price: {price_display}
🏠 Type: {prop.get('property_type', 'N/A')}
✨ Highlights: {highlights if highlights else 'None listed'}
🔗 Source: {prop.get('source', 'N/A').replace('_', ' ').title()}

"""
        
        if total > 3:
            formatted_results += f"\nThere are {total - 3} more properties available. Would you like to see more results or refine your search?"
        else:
            formatted_results += "\nWould you like me to help you contact the property owners or search for different criteria?"
        
        return formatted_results
    
    def _format_price(self, price_info: Dict[str, Any]) -> str:
        """Format price information for display"""
        if not price_info:
            return "Price not available"
        
        display = price_info.get('display', 0)
        currency = price_info.get('currency', 'IDR')
        price_type = price_info.get('type', 'rental')
        
        if currency == 'IDR':
            # Format IDR with proper separators
            formatted_price = f"IDR {display:,}"
        else:
            formatted_price = f"{currency} {display:,}"
        
        if price_type == 'rental':
            formatted_price += " per month"
        elif price_type == 'purchase':
            formatted_price += " (purchase)"
        
        return formatted_price
    
    def get_conversation_summary(self) -> str:
        """
        Generate a summary of current conversation state
        Used in phase 2 of conversation flow
        """
        state = self.conversation_state
        summary_parts = []
        
        if state.location:
            summary_parts.append(f"Location: {state.location}")
        if state.bedrooms:
            summary_parts.append(f"Bedrooms: {state.bedrooms}")
        if state.bathrooms:
            summary_parts.append(f"Bathrooms: {state.bathrooms}")
        if state.max_price:
            summary_parts.append(f"Max price: IDR {state.max_price:,}")
        if state.property_type:
            summary_parts.append(f"Type: {state.property_type}")
        if state.term:
            summary_parts.append(f"Term: {state.term}")
        if state.furnished is not None:
            summary_parts.append(f"Furnished: {'Yes' if state.furnished else 'No'}")
        if state.pool is not None:
            summary_parts.append(f"Swimming pool: {'Required' if state.pool else 'Not required'}")
        if state.pet_friendly is not None:
            summary_parts.append(f"Pet-friendly: {'Required' if state.pet_friendly else 'Not required'}")
        
        if summary_parts:
            return "Let me summarize what you're looking for:\n" + "\n".join(f"• {part}" for part in summary_parts) + "\n\nIs this correct? Should I search for properties with these criteria?"
        else:
            return "I don't have enough information yet to search for properties. Could you tell me more about what you're looking for?"
    
    def reset_conversation(self):
        """Reset conversation state for new search"""
        self.conversation_state = ConversationState()
    
    def get_available_locations(self) -> List[str]:
        """Get available locations from API"""
        try:
            response = requests.get(f"{self.api_base_url}/api/locations", timeout=10)
            if response.status_code == 200:
                return response.json().get('locations', [])
            else:
                return ["Canggu", "Seminyak", "Ubud", "Kedungu", "Jimbaran"]  # Fallback
        except Exception as e:
            logger.error(f"Error getting locations: {str(e)}")
            return ["Canggu", "Seminyak", "Ubud", "Kedungu", "Jimbaran"]  # Fallback
