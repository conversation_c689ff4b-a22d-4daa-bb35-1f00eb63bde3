#!/usr/bin/env python3
"""
Property Schema Definitions for Firecrawl Data Extraction
Based on PRD: Firecrawl-integratie (Data Ingestie)
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

class PropertyStatus(Enum):
    AVAILABLE = "Available"
    NOT_AVAILABLE = "Not Available"
    ONLINE = "Online"
    RENTED = "Rented"

class PaymentTerm(Enum):
    MONTHLY = "monthly"
    YEARLY = "yearly"
    BOTH = "both"

@dataclass
class PropertySize:
    land_size_sqm: Optional[int] = None
    building_size_sqm: Optional[int] = None

@dataclass
class IndoorDetails:
    living_room: Optional[str] = None
    dining_room: Optional[str] = None
    kitchen: Optional[str] = None

@dataclass
class OutdoorDetails:
    swimming_pool: Optional[bool] = None
    pool_size: Optional[str] = None
    garden: Optional[bool] = None
    terrace: Optional[bool] = None

@dataclass
class MonthlyCosts:
    banjar_fee_security: Optional[str] = None
    cleaning_service: Optional[str] = None
    pool_maintenance: Optional[str] = None
    garden_maintenance: Optional[str] = None
    bin_collection: Optional[str] = None
    electricity: Optional[str] = None
    internet_included: Optional[str] = None

def get_complete_property_schema() -> Dict[str, Any]:
    """
    Complete property extraction schema for Firecrawl
    Only extracts data for AVAILABLE properties
    """
    return {
        "type": "object",
        "properties": {
            # Basic Information
            "title": {"type": "string"},
            "price": {"type": "string"},
            "payment_term": {"type": "string"},
            "location": {"type": "string"},
            "bedrooms": {"type": "integer"},
            "bathrooms": {"type": "integer"},
            "ensuite_bathrooms": {"type": "integer"},
            "description": {"type": "string"},
            
            # Property Details
            "property_type": {"type": "string"},
            "property_id": {"type": "string"},
            "status": {"type": "string"},
            "availability_date": {"type": "string"},
            "furnishing": {"type": "string"},
            "view": {"type": "string"},
            "style_design": {"type": "string"},
            "surrounding": {"type": "string"},
            "floor_level": {"type": "integer"},
            
            # Technical Specifications
            "electricity_capacity": {"type": "string"},
            "water_source": {"type": "string"},
            "parking": {"type": "string"},
            "internet": {"type": "string"},
            "air_conditioner_count": {"type": "integer"},
            
            # Policies
            "pet_policy": {"type": "string"},
            "sublease_allowed": {"type": "boolean"},
            
            # Size Information
            "size": {
                "type": "object",
                "properties": {
                    "land_size_sqm": {"type": "integer"},
                    "building_size_sqm": {"type": "integer"}
                }
            },
            "year_built": {"type": "integer"},
            
            # Room Details
            "indoor_details": {
                "type": "object",
                "properties": {
                    "living_room": {"type": "string"},
                    "dining_room": {"type": "string"},
                    "kitchen": {"type": "string"}
                }
            },
            "outdoor_details": {
                "type": "object",
                "properties": {
                    "swimming_pool": {"type": "boolean"},
                    "pool_size": {"type": "string"},
                    "garden": {"type": "boolean"},
                    "terrace": {"type": "boolean"}
                }
            },
            
            # Cost Breakdown
            "monthly_costs": {
                "type": "object",
                "properties": {
                    "banjar_fee_security": {"type": "string"},
                    "cleaning_service": {"type": "string"},
                    "pool_maintenance": {"type": "string"},
                    "garden_maintenance": {"type": "string"},
                    "bin_collection": {"type": "string"},
                    "electricity": {"type": "string"},
                    "internet_included": {"type": "string"}
                }
            },
            
            # Media & Links
            "images": {
                "type": "array",
                "items": {"type": "string"},
                "description": "Array of image URLs from the property page"
            },
            "brochure_url": {"type": "string"},
            "detail_url": {"type": "string"},
            
            # Amenities
            "amenities": {
                "type": "array",
                "items": {"type": "string"}
            }
        },
        "required": [
            "title", 
            "price", 
            "location", 
            "bedrooms", 
            "bathrooms", 
            "property_id", 
            "status",
            "detail_url"
        ]
    }

def get_availability_filter_prompt() -> str:
    """
    Prompt to only extract available properties
    """
    return """
    IMPORTANT: Only extract property data if the property status is "Available", "Online", or shows clear availability.
    
    If the property shows "Not Available", "Rented", "Sold", or any indication that it's not currently available for rent/purchase, 
    return an empty object {} instead of extracting the data.
    
    Extract ALL comprehensive property details including:
    - Title, price, payment terms, location, bedrooms, bathrooms
    - Property specifications, amenities, size information
    - Indoor/outdoor details, cost breakdowns
    - ALL IMAGE URLS found on the property page
    - Brochure links, availability dates
    - Pet policies, sublease information
    - All other relevant property details
    
    For images: Extract ALL image URLs that show the property (interior, exterior, amenities, etc.)
    """

def transform_firecrawl_output(raw_data: Dict[str, Any], source_url: str) -> Dict[str, Any]:
    """
    Transform Firecrawl output to internal data structure
    Based on PRD requirement for transformation step
    """
    if not raw_data or raw_data == {}:
        return None
    
    # Check if property is available
    status = raw_data.get('status', '').lower()
    if 'not available' in status or 'rented' in status or 'sold' in status:
        return None
    
    # Transform to internal format
    transformed = {
        "id": raw_data.get('property_id'),
        "title": raw_data.get('title'),
        "price": raw_data.get('price'),
        "location": raw_data.get('location'),
        "bedrooms": raw_data.get('bedrooms'),
        "bathrooms": raw_data.get('bathrooms'),
        "description": raw_data.get('description'),
        "amenities": raw_data.get('amenities', []),
        "additional_details": {
            "size": f"{raw_data.get('size', {}).get('building_size_sqm', 0)} m²",
            "year_built": raw_data.get('year_built'),
            "property_type": raw_data.get('property_type'),
            "furnishing": raw_data.get('furnishing'),
            "view": raw_data.get('view'),
            "style_design": raw_data.get('style_design'),
            "electricity_capacity": raw_data.get('electricity_capacity'),
            "water_source": raw_data.get('water_source'),
            "parking": raw_data.get('parking'),
            "internet": raw_data.get('internet'),
            "pet_policy": raw_data.get('pet_policy'),
            "sublease_allowed": raw_data.get('sublease_allowed'),
            "indoor_details": raw_data.get('indoor_details', {}),
            "outdoor_details": raw_data.get('outdoor_details', {}),
            "monthly_costs": raw_data.get('monthly_costs', {})
        },
        "images": raw_data.get('images', []),
        "brochure_url": raw_data.get('brochure_url'),
        "source_url": source_url,
        "scraped_at": None,  # Will be set by ingestion service
        "status": raw_data.get('status')
    }
    
    return transformed
